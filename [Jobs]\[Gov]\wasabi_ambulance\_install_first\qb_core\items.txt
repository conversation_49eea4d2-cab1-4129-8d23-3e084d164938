    medbag        = { name = 'medbag',        label = 'Medical Bag',    weight = 2500, type = 'item', image = 'medbag.png',     unique = false, useable = true,  shouldClose = true, combinable = nil,   description = 'A bag of medic tools' },
    tweezers      = { name = 'tweezers',      label = 'Tweezers',       weight = 50,   type = 'item', image = 'tweezers.png',   unique = false, useable = true,  shouldClose = true, combinable = nil,   description = 'For picking out bullets' },
    suturekit     = { name = 'suturekit',     label = 'Suture Kit',     weight = 60,   type = 'item', image = 'suturekit.png',  unique = false, useable = true,  shouldClose = true, combinable = nil,   description = 'For stitching your patients' },
    icepack       = { name = 'icepack',       label = 'Ice Pack',       weight = 110,  type = 'item', image = 'icepack.png',    unique = false, useable = true,  shouldClose = true, combinable = nil,   description = 'To help reduce swelling' },
    burncream     = { name = 'burncream',     label = 'Burn Cream',     weight = 125,  type = 'item', image = 'burncream.png',  unique = false, useable = true,  shouldClose = true, combinable = nil,   description = 'To help with burns' },
    defib         = { name = 'defib',         label = 'Defibrillator',  weight = 1120, type = 'item', image = 'defib.png',      unique = false, useable = true,  shouldClose = true, combinable = nil,   description = 'Used to revive patients' },
    sedative      = { name = 'sedative',      label = 'Sedative',       weight = 20,   type = 'item', image = 'sedative.png',   unique = false, useable = true,  shouldClose = true, combinable = nil,   description = 'If needed, this will sedate patient' },
    morphine30    = { name = 'morphine30',    label = 'Morphine 30MG',  weight = 2,    type = 'item', image = 'morphine30.png', unique = false, useable = true,  shouldClose = true, combinable = true,  description = 'A controlled substance to control pain' },
    morphine15    = { name = 'morphine15',    label = 'Morphine 15MG',  weight = 2,    type = 'item', image = 'morphine15.png', unique = false, useable = true,  shouldClose = true, combinable = true,  description = 'A controlled substance to control pain' },
    perc30        = { name = 'perc30',        label = 'Percocet 30MG',  weight = 2,    type = 'item', image = 'perc30.png',     unique = false, useable = true,  shouldClose = true, combinable = true,  description = 'A controlled substance to control pain' },
    perc10        = { name = 'perc10',        label = 'Percocet 10MG',  weight = 2,    type = 'item', image = 'perc10.png',     unique = false, useable = true,  shouldClose = true, combinable = true,  description = 'A controlled substance to control pain' },
    perc5         = { name = 'perc5',         label = 'Percocet 5MG',   weight = 2,    type = 'item', image = 'perc5.png',      unique = false, useable = true,  shouldClose = true, combinable = true,  description = 'A controlled substance to control pain' },
    vic10         = { name = 'vic10',         label = 'Vicodin 10MG',   weight = 2,    type = 'item', image = 'vic10.png',      unique = false, useable = true,  shouldClose = true, combinable = true,  description = 'A controlled substance to control pain' },
    vic5          = { name = 'vic5',          label = 'Vicodin 5MG',    weight = 2,    type = 'item', image = 'vic5.png',       unique = false, useable = true,  shouldClose = true, combinable = true,  description = 'A controlled substance to control pain' },
    medikit          = { name = 'medikit',          label = 'Medical Kit',    weight = 110,    type = 'item', image = 'medikit.png',       unique = false, useable = true,  shouldClose = true, combinable = true,  description = 'A first aid kit for healing injured people.' },