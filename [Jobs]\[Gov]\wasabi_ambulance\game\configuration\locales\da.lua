-----------------For support, scripts, and more----------------
--------------- https://discord.gg/wasabiscripts  -------------
---------------------------------------------------------------
if Config.Language ~= 'da' then return end

Strings = {
    no_society_account = '[^3ADVARSEL^0] Samfundskonto eksisterer ikke for: %s',
    ems_worker = 'Ambulancearbejder',
    not_authorized = 'Ikke autoriseret',
    not_authorized_desc = 'Du har ikke tilladelse til at bruge dette!',
    not_on_duty = 'Ikke på vagt',
    not_on_duty_desc = 'Du er ikke på vagt!',
    gps_enabled = 'GPS aktiveret',
    gps_enabled_desc = 'Du har aktiveret din GPS',
    gps_disabled = 'GPS deaktiveret',
    gps_disabled_desc = 'Du har deaktiveret din GPS',

    possible_cause = 'Mulig årsag',

    full_name = 'Navn',
    pulse = 'Puls',
    bpm = 'BPM: %s',
    bleed_rate = 'Blødningsrate',
    bleed_rate_low = 'Lav',
    bleed_rate_medium = 'Medium',
    bleed_rate_high = 'Høj',
    no_name = 'Intet navn',
    injuries = 'Nuværende skader',


    get_off_stretcher_ui = '[E] - Stå af båre',
    move = 'Flyt',
    move_desc = 'Flyt båren',
    put_in_vehicle = 'Læg i køretøj',
    stretcher_in_vehicle = 'Anbring båre',
    put_in_vehicle_desc = 'Læg båren i et køretøj',
    place_on_stretcher = 'Placer patient',
    place_on_stretcher_desc = 'Placer en patient på båren',
    remove_from_stretcher = 'Fjern patient',
    remove_from_stretcher_desc = 'Fjern en patient fra båren',
    not_found = 'Ikke fundet',
    already_occupied_ambulance = 'Allerede optaget',
    already_occupied_ambulance_desc = 'Denne ambulance er allerede optaget!',
    already_occupied_stretcher = 'Allerede optaget',
    already_occupied_stretcher_desc = 'Denne båre er allerede optaget!',
    vehicle_occupied = 'Køretøj optaget',
    vehicle_occupied_desc = 'Man kan ikke gøre dette, mens der er en person, der kører!',
    not_occupied_stretcher = 'Ikke optaget',
    not_occupied_stretcher_desc = 'Denne båre er ikke optaget!',
    stretcher_placement_blocked = 'Placering blokeret',
    stretcher_placement_blocked_desc = 'Du kan ikke placere båren her!',
    knocked_out = 'Knockout',
    knocked_out_desc = 'Du blev slået ud kold!',
    checkin_cooldown = 'Indtjekningsnedkøling',
    checkin_cooldown_desc = 'Du forsøger at tjekke ind for hurtigt, vent et øjeblik.',
    checkingin_progress = 'Indtjekning i gang. . .',
    remove_dead_target = 'Fjern bevidstløs person',
    ply_injury_head = 'hoved',
    ply_injury_neck = 'nakke',
    ply_injury_spine = 'rygsøjle',
    ply_injury_upper = 'øverste krop',
    ply_injury_lower = 'nedre krop',
    ply_injury_left_arm = 'venstre arm',
    ply_injury_left_leg = 'venstre ben',
    ply_injury_right_arm = 'højre arm',
    ply_injury_right_leg = 'højre ben',
    diagnosing_patient_progress = 'Diagnosticering af patient. . .',
    treating_patient_progress = 'Behandling af patient. . .',
    recovering_progress = 'Gendannelse. . .',
    injury_report = 'Skaderapport',
    none = 'Ingen',
    mild = 'Let',
    moderate = 'Moderat',
    severe = 'Alvorlig',
    deadly = 'Dødelig',
    injury_report_locations = 'Sted',
    injury_report_type = 'Skadetype: %s',
    injury_report_severity = 'Alvorlighed: %s',
    injury_report_bleed = 'Blodtab',
    light_injury_title = 'Let skade',
    moderate_injury_title = 'Moderat skade',
    extreme_injury_title = 'Ekstrem skade',
    injury_bleed_notify = 'Advarsel om skade og blødning',
    light_injury_desc = 'Din %s er skadet, overvej at besøge en læge!',
    moderate_injury_desc = 'Din %s er meget skadet, du har brug for en læge!',
    extreme_injury_desc = 'Din %s er ekstremt skadet. Du skal komme til lægen, inden det er for sent!',
    injury_msg_one = 'Du har en betydelig skade.',
    injury_msg_two = 'Du har en alvorlig skade.',
    injury_msg_three = 'Du har en alvorlig skade.',
    injury_msg_four = 'Du har en kritisk skade.',
    bleed_msg_one = 'Du bløder.',
    bleed_msg_two = 'Du bløder kraftigt. Påfør noget pres.',
    bleed_msg_three = 'Du vil bløde ud!',
    fainted_title = 'Besvimet',
    fainted_so_high_desc = 'Du besvimede af at være så høj.',
    cant_jump_title = 'Kan ikke hoppe!',
    cant_jump_desc = 'Du er for skadet til at forsøge at hoppe.',
    blackout_title = 'Mistet bevidstheden',
    blackout_desc = 'Du mistede bevidstheden på grund af din %s skade! Søg øjeblikkeligt lægehjælp!',
    treated_fully_desc = 'Du er blevet behandlet og har det bedre end nogensinde før!',
    treated_not_fully_desc = 'Du er blevet behandlet, men har brug for yderligere behandling.',
    prescription_menu = 'Receptmenu',
    prescription_menu_desc = 'Adgang og håndtering af recepter',
    no_stretcher_detected = 'Ingen båre fundet',
    no_stretcher_detected_desc = 'Der blev ikke fundet en aktiv båre!',
    cant_run = 'Kan ikke sprinte',
    cant_run_desc = 'Du er for skadet til at sprinte!',
    no_back_seat = 'Ingen ledige sæder',
    no_back_seat_desc = 'Der er ingen sæder på bagsiden af denne ambulance.',
    enter_vehicle_back = 'Kom ind i ambulance (bagpå)',
    stretcher_already_deployed = 'Båre allerede udlagt',
    stretcher_already_deployed_desc = 'Båren, der var tildelt denne ambulance, er allerede blevet fjernet.',
    send_stretcher_home = 'Vend tilbage til køretøjet',
    ambulance_not_found = 'Båre blev fjernet, men ambulancen blev ikke fundet til tilbagevenden!',
    bleedout = 'blødning',
    player_injury_critical_desc = 'Spilleren er i kritisk tilstand på grund af en %s skade!',
    gps_active = 'GPS aktiveret',
    gps_active_desc = 'Spilleren %s aktiverede deres GPS',
    gps_deactive = 'GPS deaktiveret',
    gps_deactive_desc = 'Spilleren %s deaktiverede deres GPS',
    no_wsb = '^0[^3ADVARSEL^0] wasabi_bridge blev IKKE startet EFTER framework og/eller FØR ressourcen: %s',
    spawn_blocked = 'Garage blokeret',
    spawn_blocked_desc = 'Du kan ikke trække dit køretøj ud, fordi det er blokeret!',
    menu_remove_crutch = 'Fjern krykke',
    menu_remove_crutch_desc = 'Fjern krykke til en nærliggende patient',
    menu_remove_chair = 'Fjern kørestol',
    menu_remove_chair_desc = 'Fjern kørestol til en nærliggende patient',
    toggle_stretcher = 'Tag båre',
    toggle_stretcher_desc = 'Tag båren fra det nærmeste køretøj',
    no_vehicle_nearby = 'Køretøj',
    no_vehicle_nearby_desc = 'Der er ingen køretøj i nærheden',
    no_ambulance_nearby_desc = 'Der er ingen ambulance i nærheden',
    on_duty = 'På vagt',
    on_duty_desc = 'Du har lige skiftet til på vagt-tilstand!',
    off_duty = 'Ikke på vagt',
    off_duty_desc = 'Du har lige skiftet til ikke på vagt-tilstand!',
    amount = 'Beløb',
    mr = 'Hr',
    mrs = 'Fru',
    debt_collection = 'Gældsinddrivelse',
    db_email =
    'Kære %s %s, <br /><br />Central Judicial Collection Agency (CJCA) har opkrævet de bøder, du har modtaget fra politiet.<br />Der er trukket <strong>$%s</strong> fra din konto.<br /><br />Med venlig hilsen,<br />Hr. Wasabi',
    fine_sent = 'Bøder sendt',
    fine_sent_desc = 'Du har succesfuldt sendt bøde på $%s!',
    fine_received = 'Modtaget bøde',
    fine_received_desc = 'Du har modtaget en bøde på $%s',
    log_killed_title = 'Spiller dræbt',
    log_killed_desc =
    '> *%s [%s] dræbte %s [%s]\n\n**Drabsmandens information:**\nNavn: `%s`\nSpiller ID: `%s`\nIdentifikator: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicens: `%s`\nLicens2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n**Ofrets information:**\nKarakternavn: `%s`\nSpiller ID: `%s`\nIdentifikator: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicens: `%s`\nLicens2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n> **Dødsårsag:** `%s`',
    log_suicide_title = 'Spiller begik selvmord',
    log_suicide_desc =
    '> %s [%s] begik selvmord\n\n**Spillerinformation:**\nNavn: `%s`\nSpiller ID: `%s`\nIdentifikator: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicens: `%s`\nLicens2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n> **Dødsårsag:** `%s`',
    unknown = 'UKENDT',
    log_admin_revive_title = 'Administrator genoplivede spiller',
    log_admin_revive_desc =
    '> *%s [%s] administrator genoplivede %s [%s]\n\n**Målets information:**\nNavn: `%s`\nSpiller ID: `%s`\nIdentifikator: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicens: `%s`\nLicens2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n**Administratorinformation:**\nKarakternavn: `%s`\nSpiller ID: `%s`\nIdentifikator: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicens: `%s`\nLicens2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    log_self_revive_title = 'Administrator genoplivede sig selv',
    log_self_revive_desc =
    '> %s [%s] genoplivede sig selv\n\n**Spillerinformation:**\nNavn: `%s`\nSpiller ID: `%s`\nIdentifikator: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicens: `%s`\nLicens2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    log_death_logs = 'Dødslogs',
    log_revive_logs = 'Genoplivningslogs',
    log_combat_logs = 'Kamp Logfiler',
    log_combatlog_title = 'Kamp Log',
    log_combatlog_desc =
    '> %s [%s] kamp log \n\n**Spillerinformation:**\nNavn: `%s`\nSpiller ID: `%s`\nIdentifikator: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicense: `%s`\nLicense2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    medbag_crutch = 'Krykke',
    medbag_crutch_desc = 'En krykke til at hjælpe patienter med at gå',
    menu_crutch = 'Anvend krykke',
    menu_crutch_desc = 'Tilføj en krykke til en nærliggende patient',
    medbag_chair = 'Kørestol',
    medbag_chair_desc = 'En kørestol til at hjælpe patienter, der ikke kan gå',
    menu_chair = 'Anvend kørestol',
    menu_chair_desc = 'Tilføj en kørestol til en nærliggende patient',
    shot = 'skud',
    stabbed = 'stik',
    beat = 'slag',
    burned = 'forbrændt',
    other = 'ukendt',
    JobMenuTitle = 'EMS-menu',
    dispatch = 'Dispatch',
    dispatch_desc = 'Tjek for spillere, der har brug for hjælp',
    DispatchMenuTitle = 'Dispatch',
    GoBack = 'Gå tilbage',
    key_map_text = 'EMS-jobmenu',
    assistance_title = 'Hjælp anmodet',
    assistance_desc = 'En person har anmodet om lægehjælp!',
    respawn_available_in = 'Respawn tilgængelig om ~r~%s minutter %s sekunder~s~\n',
    respawn_bleedout_in = 'Du vil udbløde om ~r~%s minutter %s sekunder~s~\n',
    respawn_bleedout_prompt = 'Hold [~r~E~s~] for at respawnere',
    distress_send = 'Tryk på [~r~G~s~] for at sende nødsignal til EMS',
    distress_sent_title = 'Hjælp anmodet',
    distress_sent_desc = 'Nødsignal er sendt til tilgængelige enheder!',
    route_set_title = 'Rute sat',
    route_set_desc = 'En rute er blevet sat til den nødstedte person!',
    diagnose_patient = 'Diagnose',
    diagnose_patient_desc = 'Diagnosticer den nærmeste skadede person',
    no_requests = 'Ingen aktive anmodninger',
    revive_patient = 'Genopliver',
    revive_patient_desc = 'Forsøg at genoplive en nærliggende person',
    heal_patient = 'Helbreder',
    heal_patient_desc = 'Forsøg at helbrede en nærliggende person',
    sedate_patient = 'Sedat',
    sedate_patient_desc = 'Midlertidigt sedatere en nærliggende person',
    drag_patient = 'Træk',
    drag_patient_desc = 'Træk en nærliggende skadet person',
    place_stretcher_target = 'Tilføj eller fjern patient',
    place_patient = 'Placer/i køretøj',
    place_patient_desc = 'Placer bevidstløs nærliggende person inde/ude af køretøjet',
    no_nearby = 'Ikke Fundet',
    no_nearby_desc = 'Der ser ikke ud til at være nogen omkring',
    no_injury = 'Ingen Skade',
    no_injury_desc = 'Personen ser ikke ud til at have brug for nogen behandling',
    no_injury_dead_desc =
    'Patienten ser ikke ud til at have brug for yderligere behandling. Prøv at bruge en hjertestarter og håb på det bedste!',
    player_injury = 'Person Skadet',
    player_injury_desc = 'Denne person ser ud til at have en %s sår',
    player_not_unconscious = 'Spiller Bevidst',
    player_not_unconscious_desc = 'Det ser ud til, at spilleren er ved bevidsthed',
    player_unconscious = 'Spiller Bevidstløs',
    player_unconscious_desc = 'Patienten skal være ved bevidsthed for denne behandling!',
    player_reviving = 'Genoplivning',
    player_reviving_desc = 'Genoplivning af patienten er i gang',
    player_noitem = 'Mangler Genstand',
    player_noitem_desc = 'Du mangler den påkrævede genstand til dette!',
    player_successful_revive = 'Genoplivet',
    player_successful_revive_reward_desc = 'Du har succesfuldt genoplivet patienten og tjent $%s!',
    player_successful_revive_desc = 'Du har succesfuldt genoplivet patienten!',
    player_healing = 'Behandling',
    player_healing_desc = 'Behandling af patienten er i gang',
    player_successful_heal = 'Healet',
    player_successful_heal_desc = 'Patienten er succesfuldt healet!',
    player_healed_desc = 'Du er blevet succesfuldt healet af lægen!',
    not_medic = 'Uautoriseret',
    not_medic_desc = 'Du er ikke uddannet til at bruge dette!',
    target_sedated = 'Sedated',
    target_sedated_desc = 'Du er blevet bedøvet af en sundhedsperson',
    player_successful_sedate_desc = 'Du har succesfuldt bedøvet patienten',
    drop_bag_ui = '[E] - Drop Taske',
    drop_stretch_ui = '[E] - Placer Båre',
    pickup_bag_target = 'Saml Op',
    move_target = 'Flyt',
    interact_bag_target = 'Åben',
    successful = 'Succesfuld',
    medbag_pickup = 'Du samlede medicintasken op',
    medbag_pickup_civ = 'Du søgte i tasken og har taget, hvad der kunne være nyttigt',
    stretcher_pickup = 'Du sendte båren tilbage til det ambulance, den blev trukket ud fra',
    medbag = 'Medicintaske',
    medbag_tweezers = 'Pincet',
    medbag_tweezers_desc = 'Bruges til at fjerne kugler',
    medbag_suture = 'Syningssæt',
    medbag_suture_desc = 'Bruges til at sy sår',
    medbag_icepack = 'Kølepose',
    medbag_icepack_desc = 'Bruges til at reducere hævelse',
    medbag_burncream = 'Forbrændingscreme',
    medbag_burncream_desc = 'Bruges til at behandle forbrændinger',
    medbag_defib = 'Hjertestarter',
    medbag_defib_desc = 'Til genoplivning af patienter',
    medbag_medikit = 'Medikit',
    medbag_medikit_desc = 'Bruges til at heale patienter',
    medbag_sedative = 'Bedøvelsesmiddel',
    medbag_sedative_desc = 'Bruges til at bedøve patienter',
    medbag_stretcher = 'Foldbar Båre',
    medbag_stretcher_desc = 'Bruges til at flytte patienter',
    item_grab = 'Du har taget et værktøj fra din medicintaske',
    wrong_equipment = 'Forkert Udstyr!',
    wrong_equipment_desc = 'Er du overhovedet uddannet?',
    player_not_injured = 'Ikke Skadet',
    player_not_injured_desc =
    'Denne person ser ikke ud til at have brug for yderligere behandling og er klar til hjertestarteren',
    player_treated = 'Behandlet',
    player_treated_desc = 'Du har succesfuldt behandlet patienten',
    revive_command_help = 'En admin-kommando for at genoplive spillere',
    revive_command_arg = 'Spillerens ID',
    reviveall_command_help = 'En admin-kommando for at genoplive alle spillere',
    revivearea_command_help = 'En admin-kommando for at genoplive spillere i nærheden',
    revivearea_command_arg = 'Område til genoplivning af spillere',
    resetdeathcount_command_help = 'En admin-kommando for at nulstille antallet af dødsfald for spillere',
    resetdeathcount_command_arg = 'Spillerens ID',
    viewdeathcount_command_help = 'En kommando for at se dit antal dødsfald',
    alive_again = 'Levende Igen',
    alive_again_desc = 'Du blev afleveret af en lokal på hospitalet!',
    request_supplies_target = 'Medicinske Fornødenheder',
    currency = '$',
    not_enough_funds = 'Utilstrækkelige Midler',
    not_enough_funds_desc = 'Du har ikke nok midler!',
    hospital_garage = 'Hospitalsgarage',
    used_meditkit = 'Brugt Medkit',
    used_medikit_desc = 'Du har formået at lappe dig selv sammen',
    action_cancelled = 'Handling Annulleret',
    action_cancelled_desc = 'Du annullerede din sidste handling!',
    healing_self_prog = 'Behandler Sår',
    checkin_hospital = 'Succes',
    checkin_hospital_desc = 'Du er blevet behandlet med succes af hospitalets personale',
    max_ems = 'Maksimalt Antal Ambulancereddere',
    max_ems_desc = 'Der er masser af ambulancereddere i byen! Send nødsignal for hjælp!',
    player_busy = 'Beskæftiget',
    player_busy_desc = 'Du er i øjeblikket for travlt til at udføre denne handling!',
    cloakroom = 'Omklædningsrum',
    civilian_wear = 'Civil Beklædning',
    bill_patient = 'Fakturer Patient',
    bill_patient_desc = 'Send en faktura til en nær patient',
    invalid_entry = 'Ugyldig',
    invalid_entry_desc = 'Dit indtastning var ugyldigt!',
    medical_services = 'Medicinske Tjenester',
    hospital = 'Hospital',
    interact_stretcher_ui = '[E] - Interager',
    stretcher_menu_title = 'Interaktioner med bårer',
    open_shop_ui = '[E] - Åben Apotek'
}

UIStrings = {
    player_dying = "DU ER VED AT DØ",
    player_passed = "DU ER GÅET BORT",
    ems_on_the_way = "Ambulancen er på vej!",
    press_ems_services = "for Ambulancen",
    press_for_light = "for at se lyset",
    hold = "Hold",
    time_to_respawn = "Tid til genstart",
    press = "Tryk",
    player_hurt_critical = "Kritisk tilstand!",
    player_hurt_severe = "Du er alvorligt såret",
    player_hurt_unconscious = "Bevidstløs",
    player_hurt_unconscious_direct = "Du er bevidstløs",
    player_hurt_find_help_or_ems = "Tryk på <span class='color'>G</span> for at anmode om nødtjenester",
    player_hurt_time_to_live = "Bløder ud",
    player_hurt_auto_respawn = "Livstegn aftager",
    player_hurt_respawn = "Tryk på E for at se lyset",

    ems_online = "HJÆLP ER ONLINE",
    ems_offline = "HJÆLP IKKE TILGÆNGELIG",
    currently_online = "AKTUELT ONLINE: "
}
