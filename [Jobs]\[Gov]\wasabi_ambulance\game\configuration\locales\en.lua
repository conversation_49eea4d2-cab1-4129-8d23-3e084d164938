-----------------For support, scripts, and more----------------
--------------- https://discord.gg/wasabiscripts  -------------
---------------------------------------------------------------
if not Config.Language then Config.Language = 'en' end
if Config.Language ~= 'en' then return end

Strings = {
    no_society_account = '[^3WARNING^0] Society account does not exist for: %s',
    ems_worker = 'EMS Worker',
    not_authorized = 'Not Authorized',
    not_authorized_desc = 'You are not authorized to use this!',
    not_on_duty = 'Not On Duty',
    not_on_duty_desc = 'You are not on duty!',
    gps_enabled = 'GPS Enabled',
    gps_enabled_desc = 'You have enabled your GPS',
    gps_disabled = 'GPS Disabled',
    gps_disabled_desc = 'You have disabled your GPS',

    possible_cause = 'Possible Cause',

    full_name = 'Name',
    pulse = 'BPM',
    bpm = 'BPM: %s',
    bleed_rate = 'Bleed Rate',
    bleed_rate_low = 'Low',
    bleed_rate_medium = 'Medium',
    bleed_rate_high = 'High',
    no_name = 'No Name',
    injuries = 'Current Injuries',

    get_off_stretcher_ui = '[E] - Get Off Stretcher',
    move = 'Move',
    move_desc = 'Move the stretcher',
    put_in_vehicle = 'Put in Vehicle',
    stretcher_in_vehicle = 'Deposit Stretcher',
    put_in_vehicle_desc = 'Put the stretcher in a vehicle',
    place_on_stretcher = 'Place Patient',
    place_on_stretcher_desc = 'Place a patient on the stretcher',
    remove_from_stretcher = 'Remove Patient',
    remove_from_stretcher_desc = 'Remove a patient from the stretcher',
    not_found = 'Not Found',
    already_occupied_ambulance = 'Already Occupied',
    already_occupied_ambulance_desc = 'This ambulance is already occupied!',
    already_occupied_stretcher = 'Already Occupied',
    already_occupied_stretcher_desc = 'This stretcher is already occupied!',
    vehicle_occupied = 'Vehicle Occupied',
    vehicle_occupied_desc = 'There can not be a person driving while doing this!',
    not_occupied_stretcher = 'Not Occupied',
    not_occupied_stretcher_desc = 'This stretcher is not occupied!',
    stretcher_placement_blocked = 'Placement Blocked',
    stretcher_placement_blocked_desc = 'You can not place the stretcher here!',

    knocked_out = 'Knocked Out',
    knocked_out_desc = 'You were knocked out cold!',
    checkin_cooldown = 'Check-in Cooldown',
    checkin_cooldown_desc = 'You are attempting to check-in too fast, please wait a moment.',
    checkingin_progress = 'Checking In. . .',
    remove_dead_target = 'Remove Unconscious Person',
    ply_injury_head = 'head',
    ply_injury_neck = 'neck',
    ply_injury_spine = 'spine',
    ply_injury_upper = 'upper body',
    ply_injury_lower = 'lower body',
    ply_injury_left_arm = 'left arm',
    ply_injury_left_leg = 'left leg',
    ply_injury_right_arm = 'right arm',
    ply_injury_right_leg = 'right leg',
    diagnosing_patient_progress = 'Diagnosing Patient. . .',
    treating_patient_progress = 'Treating Patient. . .',
    recovering_progress = 'Recovering. . .',
    injury_report = 'Injury Report',
    none = 'None',
    mild = 'Mild',
    moderate = 'Moderate',
    severe = 'Severe',
    deadly = 'Deadly',
    injury_report_locations = 'Location',
    injury_report_type = 'Injury Type: %s',
    injury_report_severity = 'Severity: %s',
    injury_report_bleed = 'Blood Loss',
    light_injury_title = 'Light Injury',
    moderate_injury_title = 'Moderate Injury',
    extreme_injury_title = 'Extreme Injury',
    injury_bleed_notify = 'Injury & Bleeding Alert',
    light_injury_desc = 'Your %s is injured, consider visiting a doctor!',
    moderate_injury_desc = 'Your %s is very injured, you need a doctor!',
    extreme_injury_desc = 'Your %s is tremendously injured. You need to get to the doctor before it is too late!',
    injury_msg_one = 'You have a significant injury.',
    injury_msg_two = 'You have a serious injury.',
    injury_msg_three = 'You have a severe injury.',
    injury_msg_four = 'You have a critical injury.',
    bleed_msg_one = 'You\'re bleeding.',
    bleed_msg_two = 'You\'re bleeding badly. Apply some pressure.',
    bleed_msg_three = 'You\'re going to bleed out!',
    fainted_title = 'Fainted',
    fainted_so_high_desc = 'You fainted from being so high.',
    cant_jump_title = 'Can\'t Jump!',
    cant_jump_desc = 'You are too injured to attempt to jump',
    blackout_title = 'Lost Consciousness',
    blackout_desc = 'You lost consciousness due to your %s injury! Seek medical attention immediately!',
    treated_fully_desc = 'You have been treated and are feeling better than ever!',
    treated_not_fully_desc = 'You have been treated but need additional treatment.',
    prescription_menu = 'Prescriptions Menu',
    prescription_menu_desc = 'Access and manage prescriptions',
    no_stretcher_detected = 'No Stretcher',
    no_stretcher_detected_desc = 'There was not an active stretcher detected!',
    cant_run = 'Can\'t Sprint',
    cant_run_desc = 'You are too injured to sprint!',
    no_back_seat = 'No Available Seats',
    no_back_seat_desc = 'There are no seats in the back of this ambulance',
    enter_vehicle_back = 'Enter Ambulance (Rear)',
    stretcher_already_deployed = 'Stretcher Already Deployed',
    stretcher_already_deployed_desc = 'The stretcher allocated to this ambulance has already been removed.',
    send_stretcher_home = 'Return To Vehicle',
    ambulance_not_found = 'Stretcher was removed but the ambulance was not found to return to!',
    bleedout = 'bleeding',
    player_injury_critical_desc = 'Player is in critical condition due to a %s wound!',
    gps_active = 'GPS Activated',
    gps_active_desc = 'Player %s activated their GPS',
    gps_deactive = 'GPS Deactivated',
    gps_deactive_desc = 'Player %s deactivated their GPS',
    no_wsb = '^0[^3WARNING^0] wasabi_bridge was NOT started AFTER framework and/or BEFORE resource: %s',
    spawn_blocked = 'Garage Blocked',
    spawn_blocked_desc = 'You can not pull your vehicle out because it is blocked!',
    menu_remove_crutch = 'Remove Crutch',

    menu_remove_crutch_desc = 'Remove a crutch to a nearby patient',
    menu_remove_chair = 'Remove Wheelchair',
    menu_remove_chair_desc = 'Remove a wheelchair to a nearby patient',
    toggle_stretcher = 'Take Stretcher',
    toggle_stretcher_desc = 'Take stretcher from nearest vehicle',
    no_vehicle_nearby = 'Vehicle',
    no_vehicle_nearby_desc = 'There is no vehicle nearby',
    no_ambulance_nearby_desc = 'There is no ambulance nearby',
    on_duty = 'On Duty',
    on_duty_desc = 'You have just switched to on duty!',
    off_duty = 'Off Duty',
    off_duty_desc = 'You have just switched to off duty!',
    amount = 'Amount',
    mr = 'Mr',
    mrs = 'Mrs',
    debt_collection = 'Debt Collection',
    db_email =
    'Dear %s %s, <br /><br />The Central Judicial Collection Agency (CJCA) charged the fines you received from the police.<br />There is <strong>$%s</strong> withdrawn from your account.<br /><br />Kind regards,<br />Mr. Wasabi',
    fine_sent = 'Fines Sent',
    fine_sent_desc = 'You have successfully sent fine for $%s!',
    fine_received = 'Fine Received',
    fine_received_desc = 'You have received a fine for $%s',
    log_killed_title = 'Player Killed',
    log_killed_desc =
    '> *%s [%s] killed %s [%s]\n\n**Killer\'s Information:**\nName: `%s`\nPlayer ID: `%s`\nIdentifier: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicense: `%s`\nLicense2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n**Victim\'s Information:**\nCharacter Name: `%s`\nPlayer ID: `%s`\nIdentifier: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicense: `%s`\nLicense2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n> **Death Cause:** `%s`',
    log_suicide_title = 'Player Committed Suicide',
    log_suicide_desc =
    '> %s [%s] killed themselves\n\n**Player Information:**\nName: `%s`\nPlayer ID: `%s`\nIdentifier: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicense: `%s`\nLicense2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n> **Death Cause:** `%s`',
    unknown = 'UNKNOWN',
    log_admin_revive_title = 'Player Admin Revived',
    log_admin_revive_desc =
    '> *%s [%s] admin revived %s [%s]\n\n**Target\'s Information:**\nName: `%s`\nPlayer ID: `%s`\nIdentifier: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicense: `%s`\nLicense2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n**Admin\'s Information:**\nCharacter Name: `%s`\nPlayer ID: `%s`\nIdentifier: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicense: `%s`\nLicense2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    log_self_revive_title = 'Admin Self Revived',
    log_self_revive_desc =
    '> %s [%s] self revived\n\n**Player Information:**\nName: `%s`\nPlayer ID: `%s`\nIdentifier: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicense: `%s`\nLicense2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    log_death_logs = 'Death Logs',
    log_revive_logs = 'Revive Logs',
    log_combat_logs = 'Combat Logs',
    log_combatlog_title = 'Combat Log',
    log_combatlog_desc =
    '> %s [%s] combat log \n\n**Player Information:**\nName: `%s`\nPlayer ID: `%s`\nIdentifier: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicense: `%s`\nLicense2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    medbag_crutch = 'Crutch',
    medbag_crutch_desc = 'A crutch to assist patients with walking',
    menu_crutch = 'Apply Crutch',
    menu_crutch_desc = 'Add a crutch to a nearby patient',
    medbag_chair = 'Wheelchair',
    medbag_chair_desc = 'A wheelchair to assist patients who cannot walk',
    menu_chair = 'Apply Wheelchair',
    menu_chair_desc = 'Add a wheelchair to a nearby patient',
    shot = 'gunshot',
    stabbed = 'stab',
    beat = 'blunt-forced trama',
    burned = 'burn',
    other = 'unknown',
    JobMenuTitle = 'EMS Menu',
    dispatch = 'Dispatch',
    dispatch_desc = 'Check for players in need of assistance',
    DispatchMenuTitle = 'Dispatch',
    GoBack = 'Go Back',
    key_map_text = 'Ambulance Job Menu',
    assistance_title = 'Assistance Requested',
    assistance_desc = 'A person has requested medical assistance!',
    respawn_available_in = 'Respawn available in ~r~%s minutes %s seconds~s~\n',
    respawn_bleedout_in = 'You will bleed out in ~r~%s minutes %s seconds~s~\n',
    respawn_bleedout_prompt = 'Hold [~r~E~s~] to respawn',
    distress_send = 'Press [~r~G~s~] to send distress signal to EMS',
    distress_sent_title = 'Assistance Requested',
    distress_sent_desc = 'Distress signal has been sent to available units!',
    route_set_title = 'Route Set',
    route_set_desc = 'A waypoint has been set to the distressed person!',
    diagnose_patient = 'Diagnose',
    diagnose_patient_desc = 'Diagnose the closest injured person',
    no_requests = 'No active requests',
    revive_patient = 'Revive',
    revive_patient_desc = 'Attempt to revive nearby person',
    heal_patient = 'Heal',
    heal_patient_desc = 'Attempt to heal nearby person',
    sedate_patient = 'Sedate',
    sedate_patient_desc = 'Temporarily sedate nearby person',
    drag_patient = 'Drag',
    drag_patient_desc = 'Drag nearby injured person',
    place_stretcher_target = 'Add or Remove Patient',
    place_patient = 'Place in/out Vehicle',
    place_patient_desc = 'Place unconscious nearby person in/out of vehicle',

    no_nearby = 'Not Found',
    no_nearby_desc = 'There appears to be nobody around',
    no_injury = 'No Injury',
    no_injury_desc = 'Person doesn\'t appear to need any treatment',
    no_injury_dead_desc =
    'The patient doesn\'t appear to need additional treatment. Try to use the defibrillator and hope for the best!',
    player_injury = 'Person Injured',
    player_injury_desc = 'This person has appears to have a %s wound',
    player_not_unconscious = 'Player Conscious',
    player_not_unconscious_desc = 'It appears the player is conscious',
    player_unconscious = 'Player Unconscious',
    player_unconscious_desc = 'Patient is required to be conscious for this treatment!',
    player_reviving = 'Reviving',
    player_reviving_desc = 'Reviving patient in progress',
    player_noitem = 'Missing Item',
    player_noitem_desc = 'You lack the required item for this!',
    player_successful_revive = 'Revived',
    player_successful_revive_reward_desc = 'You have successfully revived the patient and earned $%s!',
    player_successful_revive_desc = 'You have successfully revived the patient!',
    player_healing = 'Healing',
    player_healing_desc = 'Healing patient in progress',
    player_successful_heal = 'Healed',
    player_successful_heal_desc = 'Patient successfully healed!',
    player_healed_desc = 'You have been successfully healed by the medic!',
    not_medic = 'Unauthorized',
    not_medic_desc = 'You are not trained to use this!',
    target_sedated = 'Sedated',
    target_sedated_desc = 'You have been sedated by a medical professional',
    player_successful_sedate_desc = 'You have successfully sedated the patient',
    drop_bag_ui = '[E] - Drop Bag',
    drop_stretch_ui = '[E] - Place Stretcher',
    pickup_bag_target = 'Pick Up',
    move_target = 'Move',
    interact_bag_target = 'Open',
    successful = 'Successful',
    medbag_pickup = 'You picked up the medbag',
    medbag_pickup_civ = 'You searched the bag and have taken what would be of use',
    stretcher_pickup = 'You sent the stretcher to the ambulance it was pulled from',
    medbag = 'Medical Bag',
    medbag_tweezers = 'Tweezers',
    medbag_tweezers_desc = 'Used to remove bullets',
    medbag_suture = 'Suture Kit',
    medbag_suture_desc = 'Used to stitch wounds',
    medbag_icepack = 'Ice Pack',
    medbag_icepack_desc = 'Used to reduce swelling',
    medbag_burncream = 'Burn Cream',
    medbag_burncream_desc = 'Used to treat burns',
    medbag_defib = 'Defibrillator',
    medbag_defib_desc = 'For reviving patients',
    medbag_medikit = 'Medkit',
    medbag_medikit_desc = 'Used for healing patients',
    medbag_sedative = 'Sedative',
    medbag_sedative_desc = 'Used to sedate patients',
    medbag_stretcher = 'Foldable Stretcher',
    medbag_stretcher_desc = 'Used for moving patients',
    item_grab = 'You have pulled a tool from your medical bag',
    wrong_equipment = 'Wrong Equipment!',
    wrong_equipment_desc = 'Have you even been trained?',
    player_not_injured = 'Not Injured',
    player_not_injured_desc =
    'This person does not appear to need any extra treatment and is clear for the defibrillator',
    player_treated = 'Treated',
    player_treated_desc = 'You have successfully treated the patient',
    revive_command_help = 'An admin command to revive players',
    revive_command_arg = 'The player id',
    reviveall_command_help = 'An admin command to revive every player',
    revivearea_command_help = 'An admin command to revive nearby players',
    revivearea_command_arg = 'Area to revive players',
    resetdeathcount_command_help = 'An admin command to reset players deathcount',
    resetdeathcount_command_arg = 'The player id',
    viewdeathcount_command_help = 'A command to view your deathcount',
    alive_again = 'Alive',
    alive_again_desc = 'You were dropped off by a local at the hospital!',
    request_supplies_target = 'Medical Supplies',
    currency = '$',
    not_enough_funds = 'Insufficient Funds',
    not_enough_funds_desc = 'You don\'t have enough funds!',
    hospital_garage = 'Hospital Garage',
    used_meditkit = 'Medkit Used',
    used_medikit_desc = 'You\'ve managed to patch yourself up',
    action_cancelled = 'Action Cancelled',
    action_cancelled_desc = 'You cancelled your last action!',
    healing_self_prog = 'Treating Wounds',
    checkin_hospital = 'Success',
    checkin_hospital_desc = 'You have been successful treated by the hospital staff',
    max_ems = 'Medics Avaliable',
    max_ems_desc = 'There are plenty of medics in the city! Distress for help!',
    player_busy = 'Busy',
    player_busy_desc = 'You are currently too busy to do this action!',
    cloakroom = 'Changing Room',
    civilian_wear = 'Civilian Wear',
    bill_patient = 'Bill Patient',
    bill_patient_desc = 'Send a nearby patient an invoice',
    invalid_entry = 'Invalid',
    invalid_entry_desc = 'Your entry was invalid!',
    medical_services = 'Medical Services',
    hospital = 'Hospital',
    interact_stretcher_ui = '[E] - Interact',
    stretcher_menu_title = 'Stretcher Interactions',
    open_shop_ui = '[E] - Open Pharmacy'
}

UIStrings = {
    player_dying = "YOU ARE DYING",
    player_passed = "YOU PASSED AWAY",
    ems_on_the_way = "Emergency services are on the way!",
    press_ems_services = "for Emergency Services",
    press_for_light = "to see the light",
    hold = "Hold",
    time_to_respawn = "Time left till respawn",
    press = "Press",
    player_hurt_critical = "Critical Condition!",
    player_hurt_severe = "You are severely hurt",
    player_hurt_unconscious = "Unconscious",
    player_hurt_unconscious_direct = "You are unconscious",
    player_hurt_find_help_or_ems = "Press <span class='color'>G</span> to request emergency services",
    player_hurt_time_to_live = "Bleeding out",
    player_hurt_auto_respawn = "Vitals fading",
    player_hurt_respawn = "Hold E to see the light",

    ems_online = "ASSISTANCE IS ONLINE",
    ems_offline = "ASSISTANCE UNAVAILABLE",
    currently_online = "CURRENTLY ONLINE: "
}
