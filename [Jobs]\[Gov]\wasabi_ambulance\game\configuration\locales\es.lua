-----------------For support, scripts, and more----------------
--------------- https://discord.gg/wasabiscripts  -------------
---------------------------------------------------------------
if Config.Language ~= 'es' then return end

Strings = {
    no_society_account = '[^3ADVERTENCIA^0] La cuenta de la sociedad no existe para: %s',
    ems_worker = 'Trabajador de ambulancia',
    not_authorized = 'No autorizado',
    not_authorized_desc = '¡No estás autorizado para usar esto!',
    not_on_duty = 'Fuera de servicio',
    not_on_duty_desc = '¡No estás de servicio!',
    gps_enabled = 'GPS habilitado',
    gps_enabled_desc = 'Has habilitado tu GPS',
    gps_disabled = 'GPS deshabilitado',
    gps_disabled_desc = 'Has deshabilitado tu GPS',

    possible_cause = 'Causa posible',

    full_name = 'Nombre',
    pulse = 'Pulso',
    bpm = 'BPM: %s',
    bleed_rate = 'Tasa de sangrado',
    bleed_rate_low = 'Bajo',
    bleed_rate_medium = 'Medio',
    bleed_rate_high = 'Alto',
    no_name = 'Sin nombre',
    injuries = 'Lesiones actuales',


    get_off_stretcher_ui = '[E] - Bajar de la camilla',
    move = 'Mover',
    move_desc = 'Mover la camilla',
    put_in_vehicle = 'Poner en Vehículo',
    stretcher_in_vehicle = 'Depositar Camilla',
    put_in_vehicle_desc = 'Poner la camilla en un vehículo',
    place_on_stretcher = 'Colocar Paciente',
    place_on_stretcher_desc = 'Colocar un paciente en la camilla',
    remove_from_stretcher = 'Quitar Paciente',
    remove_from_stretcher_desc = 'Quitar un paciente de la camilla',
    not_found = 'No Encontrado',
    already_occupied_ambulance = 'Ya Ocupada',
    already_occupied_ambulance_desc = '¡Esta ambulancia ya está ocupada!',
    already_occupied_stretcher = 'Ya Ocupada',
    already_occupied_stretcher_desc = '¡Esta camilla ya está ocupada!',
    vehicle_occupied = 'Vehículo Ocupado',
    vehicle_occupied_desc = '¡No se puede hacer esto con alguien conduciendo!',
    not_occupied_stretcher = 'No Ocupada',
    not_occupied_stretcher_desc = 'Esta camilla no está ocupada',
    stretcher_placement_blocked = 'Colocación Bloqueada',
    stretcher_placement_blocked_desc = '¡No puedes colocar la camilla aquí!',
    knocked_out = 'Noqueado',
    knocked_out_desc = '¡Has sido noqueado!',
    checkin_cooldown = 'Enfriamiento de Registro',
    checkin_cooldown_desc = 'Estás intentando registrarte demasiado rápido, por favor espera un momento.',
    checkingin_progress = 'Registrando...',
    remove_dead_target = 'Eliminar Persona Inconsciente',
    ply_injury_head = 'cabeza',
    ply_injury_neck = 'cuello',
    ply_injury_spine = 'columna',
    ply_injury_upper = 'parte superior del cuerpo',
    ply_injury_lower = 'parte inferior del cuerpo',
    ply_injury_left_arm = 'brazo izquierdo',
    ply_injury_left_leg = 'pierna izquierda',
    ply_injury_right_arm = 'brazo derecho',
    ply_injury_right_leg = 'pierna derecha',
    diagnosing_patient_progress = 'Diagnosticando Paciente...',
    treating_patient_progress = 'Tratando Paciente...',
    recovering_progress = 'Recuperando...',
    injury_report = 'Informe de Lesiones',
    none = 'Ninguna',
    mild = 'Leve',
    moderate = 'Moderada',
    severe = 'Grave',
    deadly = 'Mortal',
    injury_report_locations = 'Ubicación',
    injury_report_type = 'Tipo de Lesión: %s',
    injury_report_severity = 'Severidad: %s',
    injury_report_bleed = 'Pérdida de Sangre',
    light_injury_title = 'Lesión Leve',
    moderate_injury_title = 'Lesión Moderada',
    extreme_injury_title = 'Lesión Extrema',
    injury_bleed_notify = 'Alerta de Lesión y Sangrado',
    light_injury_desc = 'Tu %s está lesionado, considera visitar a un médico!',
    moderate_injury_desc = 'Tu %s está muy lesionado, necesitas un médico!',
    extreme_injury_desc = 'Tu %s está tremendamente lesionado. ¡Necesitas ir al médico antes de que sea demasiado tarde!',
    injury_msg_one = 'Tienes una lesión significativa.',
    injury_msg_two = 'Tienes una lesión grave.',
    injury_msg_three = 'Tienes una lesión severa.',
    injury_msg_four = 'Tienes una lesión crítica.',
    bleed_msg_one = 'Estás sangrando.',
    bleed_msg_two = 'Estás sangrando mucho. Aplica presión.',
    bleed_msg_three = '¡Vas a desangrarte!',
    fainted_title = 'Desmayado',
    fainted_so_high_desc = 'Te desmayaste de estar tan alto.',
    cant_jump_title = '¡No Puedes Saltar!',
    cant_jump_desc = 'Estás demasiado lesionado para intentar saltar',
    blackout_title = 'Perdida de Conciencia',
    blackout_desc = '¡Perdiste la conciencia debido a tu lesión en %s! Busca atención médica inmediatamente!',
    treated_fully_desc = 'Has sido tratado y te sientes mejor que nunca!',
    treated_not_fully_desc = 'Has sido tratado pero necesitas tratamiento adicional.',
    prescription_menu = 'Menú de Prescripciones',
    prescription_menu_desc = 'Acceder y gestionar prescripciones',
    no_stretcher_detected = 'No se Detectó Camilla',
    no_stretcher_detected_desc = '¡No se detectó una camilla activa!',
    cant_run = 'No Puede Correr',
    cant_run_desc = 'Estás demasiado lesionado para correr!',
    no_back_seat = 'No hay Asientos Disponibles',
    no_back_seat_desc = 'No hay asientos en la parte trasera de esta ambulancia',
    enter_vehicle_back = 'Entrar a la Ambulancia (Trasera)',
    stretcher_already_deployed = 'Camilla Ya Desplegada',
    stretcher_already_deployed_desc = 'La camilla asignada a esta ambulancia ya ha sido retirada.',
    send_stretcher_home = 'Regresar al Vehículo',
    ambulance_not_found = 'La camilla fue retirada pero no se encontró la ambulancia para regresar!',
    bleedout = 'sangrando',
    player_injury_critical_desc = '¡El jugador está en condición crítica debido a una herida %s!',
    gps_active = 'GPS Activado',
    gps_active_desc = 'El jugador %s activó su GPS',
    gps_deactive = 'GPS Desactivado',
    gps_deactive_desc = 'El jugador %s desactivó su GPS',
    no_wsb = '^0[^3ADVERTENCIA^0] wasabi_bridge NO se inició DESPUÉS del marco y/o ANTES del recurso: %s',
    spawn_blocked = 'Garaje Bloqueado',
    spawn_blocked_desc = '¡No puedes sacar tu vehículo porque está bloqueado!',
    menu_remove_crutch = 'Quitar Muleta',
    menu_remove_crutch_desc = 'Quitar una muleta a un paciente cercano',
    menu_remove_chair = 'Quitar Silla de Ruedas',
    menu_remove_chair_desc = 'Quitar una silla de ruedas a un paciente cercano',
    toggle_stretcher = 'Tomar camilla',
    toggle_stretcher_desc = 'Tomar la camilla del vehículo más cercano',
    no_vehicle_nearby = 'Vehículo',
    no_vehicle_nearby_desc = 'No hay ningún vehículo cerca',
    no_ambulance_nearby_desc = 'No hay ninguna ambulancia cerca',
    on_duty = 'En Servicio',
    on_duty_desc = '¡Acabas de cambiar a estado de servicio!',
    off_duty = 'Fuera de Servicio',
    off_duty_desc = '¡Acabas de cambiar a estado fuera de servicio!',
    amount = 'Cantidad',
    mr = 'Sr.',
    mrs = 'Sra.',
    debt_collection = 'Recaudación de Deudas',
    db_email =
    'Estimado/a %s %s, <br /><br />La Agencia Central de Cobro Judicial (CJCA) ha cobrado las multas que recibiste de la policía.<br />Se han retirado <strong>$%s</strong> de tu cuenta.<br /><br />Atentamente,<br />Sr. Wasabi',
    fine_sent = 'Multas Enviadas',
    fine_sent_desc = '¡Has enviado exitosamente una multa de $%s!',
    fine_received = 'Multa Recibida',
    fine_received_desc = 'Has recibido una multa de $%s',
    log_killed_title = 'Jugador Asesinado',
    log_killed_desc =
    '> *%s [%s] asesinó a %s [%s]\n\n**Información del Asesino:**\nNombre: `%s`\nID de Jugador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicencia: `%s`\nLicencia2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n**Información de la Víctima:**\nNombre del Personaje: `%s`\nID de Jugador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicencia: `%s`\nLicencia2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n> **Causa de la Muerte:** `%s`',
    log_suicide_title = 'Jugador Cometió Suicidio',
    log_suicide_desc =
    '> %s [%s] se suicidó\n\n**Información del Jugador:**\nNombre: `%s`\nID de Jugador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicencia: `%s`\nLicencia2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n> **Causa de la Muerte:** `%s`',
    unknown = 'DESCONOCIDO',
    log_admin_revive_title = 'Jugador Revivido por Administrador',
    log_admin_revive_desc =
    '> *%s [%s] revivió como administrador a %s [%s]\n\n**Información del Objetivo:**\nNombre: `%s`\nID de Jugador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicencia: `%s`\nLicencia2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n**Información del Administrador:**\nNombre del Personaje: `%s`\nID de Jugador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicencia: `%s`\nLicencia2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    log_self_revive_title = 'Auto-Revivido por Administrador',
    log_self_revive_desc =
    '> %s [%s] se auto-revivió\n\n**Información del Jugador:**\nNombre: `%s`\nID de Jugador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicencia: `%s`\nLicencia2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    log_death_logs = 'Registros de Muertes',
    log_revive_logs = 'Registros de Revividos',
    log_combat_logs = 'Registros de Combate',
    log_combatlog_title = 'Registro de Combate',
    log_combatlog_desc =
    '> %s [%s] registro de combate \n\n**Información del jugador:**\nNombre: `%s`\nID del jugador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicencia: `%s`\nLicencia2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    medbag_crutch = 'Muleta',
    medbag_crutch_desc = 'Una muleta para ayudar a los pacientes a caminar',
    menu_crutch = 'Aplicar Muleta',
    menu_crutch_desc = 'Agregar una muleta a un paciente cercano',
    medbag_chair = 'Silla de Ruedas',
    medbag_chair_desc = 'Una silla de ruedas para ayudar a los pacientes que no pueden caminar',
    menu_chair = 'Aplicar Silla de Ruedas',
    menu_chair_desc = 'Agregar una silla de ruedas a un paciente cercano',
    shot = 'disparo',
    stabbed = 'apuñalado',
    beat = 'trauma contundente',
    burned = 'quemadura',
    other = 'desconocido',
    JobMenuTitle = 'Menú EMS',
    dispatch = 'Despacho',
    dispatch_desc = 'Buscar jugadores que necesitan asistencia',
    DispatchMenuTitle = 'Despacho',
    GoBack = 'Regresar',
    key_map_text = 'Menú de Trabajo de Ambulancia',
    assistance_title = 'Asistencia Solicitada',
    assistance_desc = '¡Una persona ha solicitado asistencia médica!',
    respawn_available_in = 'Reaparición disponible en ~r~%s minutos %s segundos~s~\n',
    respawn_bleedout_in = 'Morirás por desangramiento en ~r~%s minutos %s segundos~s~\n',
    respawn_bleedout_prompt = 'Mantén presionado [~r~E~s~] para reaparecer',
    distress_send = 'Presiona [~r~G~s~] para enviar señal de auxilio a EMS',
    distress_sent_title = 'Asistencia Solicitada',
    distress_sent_desc = '¡Señal de auxilio enviada a las unidades disponibles!',
    route_set_title = 'Ruta Establecida',
    route_set_desc = 'Se ha establecido un punto de ruta a la persona en apuros',
    diagnose_patient = 'Diagnosticar',
    diagnose_patient_desc = 'Diagnosticar a la persona herida más cercana',
    no_requests = 'No hay solicitudes activas',
    revive_patient = 'Revivir',
    revive_patient_desc = 'Intentar revivir a una persona cercana',
    heal_patient = 'Curar',
    heal_patient_desc = 'Intentar curar a una persona cercana',
    sedate_patient = 'Sedar',
    sedate_patient_desc = 'Sedar temporalmente a una persona cercana',
    drag_patient = 'Arrastrar',
    drag_patient_desc = 'Arrastrar a una persona herida cercana',
    place_stretcher_target = 'Agregar o Quitar Paciente',
    place_patient = 'Colocar en/fuera del Vehículo',
    place_patient_desc = 'Colocar a una persona inconsciente cercana dentro/fuera del vehículo',
    no_nearby = 'No encontrado',
    no_nearby_desc = 'Parece que no hay nadie alrededor',
    no_injury = 'Sin lesiones',
    no_injury_desc = 'La persona no parece necesitar ningún tratamiento',
    no_injury_dead_desc =
    'El paciente no parece necesitar tratamiento adicional. ¡Intente usar el desfibrilador y espere lo mejor!',
    player_injury = 'Persona herida',
    player_injury_desc = 'Esta persona parece tener una herida %s',
    player_not_unconscious = 'Jugador consciente',
    player_not_unconscious_desc = 'Parece que el jugador está consciente',
    player_unconscious = 'Jugador inconsciente',
    player_unconscious_desc = '¡El paciente debe estar consciente para este tratamiento!',
    player_reviving = 'Reviviendo',
    player_reviving_desc = 'Reviviendo al paciente en progreso',
    player_noitem = 'Elemento faltante',
    player_noitem_desc = 'Te falta el elemento requerido para esto!',
    player_successful_revive = 'Revivido con éxito',
    player_successful_revive_reward_desc = 'Has revivido con éxito al paciente y ganado $%s!',
    player_successful_revive_desc = 'Has revivido con éxito al paciente!',
    player_healing = 'Curando',
    player_healing_desc = 'Curando al paciente en progreso',
    player_successful_heal = 'Curado con éxito',
    player_successful_heal_desc = '¡Paciente curado con éxito!',
    player_healed_desc = 'Has sido curado con éxito por el médico!',
    not_medic = 'No autorizado',
    not_medic_desc = 'No estás autorizado para usar esto!',
    target_sedated = 'Sedado',
    target_sedated_desc = 'Has sido sedado por un profesional médico',
    player_successful_sedate_desc = 'Has sedado con éxito al paciente',
    drop_bag_ui = '[E] - Soltar bolsa',
    drop_stretch_ui = '[E] - Colocar camilla',
    pickup_bag_target = 'Recoger',
    move_target = 'Mover',
    interact_bag_target = 'Abrir',
    successful = 'Exitoso',
    medbag_pickup = 'Has recogido la bolsa médica',
    medbag_pickup_civ = 'Has registrado la bolsa y has tomado lo que podría ser útil',
    stretcher_pickup = 'Has enviado la camilla a la ambulancia de la que fue tomada',
    medbag = 'Bolsa médica',
    medbag_tweezers = 'Pinzas',
    medbag_tweezers_desc = 'Usado para quitar balas',
    medbag_suture = 'Kit de sutura',
    medbag_suture_desc = 'Usado para coser heridas',
    medbag_icepack = 'Paquete de hielo',
    medbag_icepack_desc = 'Usado para reducir la hinchazón',
    medbag_burncream = 'Crema para quemaduras',
    medbag_burncream_desc = 'Usado para tratar quemaduras',
    medbag_defib = 'Desfibrilador',
    medbag_defib_desc = 'Para revivir pacientes',
    medbag_medikit = 'Botiquín médico',
    medbag_medikit_desc = 'Usado para curar pacientes',
    medbag_sedative = 'Sedante',
    medbag_sedative_desc = 'Usado para sedar pacientes',
    medbag_stretcher = 'Camilla plegable',
    medbag_stretcher_desc = 'Usado para mover pacientes',
    item_grab = 'Has sacado una herramienta de tu bolsa médica',
    wrong_equipment = 'Equipo incorrecto!',
    wrong_equipment_desc = '¿Alguna vez has sido entrenado?',
    player_not_injured = 'Sin lesiones',
    player_not_injured_desc = 'Esta persona no parece necesitar tratamiento adicional y está lista para el desfibrilador',
    player_treated = 'Tratado',
    player_treated_desc = 'Has tratado con éxito al paciente',
    revive_command_help = 'Un comando de administrador para revivir jugadores',
    revive_command_arg = 'La ID del jugador',
    reviveall_command_help = 'Un comando de administrador para revivir a todos los jugadores',
    revivearea_command_help = 'Un comando de administrador para revivir a jugadores cercanos',
    revivearea_command_arg = 'Área para revivir jugadores',
    resetdeathcount_command_help = 'Un comando de administrador para restablecer el contador de muertes de un jugador',
    resetdeathcount_command_arg = 'La ID del jugador',
    viewdeathcount_command_help = 'Un comando para ver tu contador de muertes',
    alive_again = 'Vivo de nuevo',
    alive_again_desc = '¡Un local te dejó en el hospital!',
    request_supplies_target = 'Suministros médicos',
    currency = '$',
    not_enough_funds = 'Fondos insuficientes',
    not_enough_funds_desc = '¡No tienes suficiente dinero!',
    hospital_garage = 'Garaje del hospital',
    used_meditkit = 'Usado Kit Médico',
    used_medikit_desc = 'Has logrado curarte a ti mismo',
    action_cancelled = 'Acción Cancelada',
    action_cancelled_desc = '¡Has cancelado tu última acción!',
    healing_self_prog = 'Tratando Heridas',
    checkin_hospital = 'Éxito',
    checkin_hospital_desc = 'Has sido tratado con éxito por el personal del hospital',
    max_ems = 'Médicos Disponibles',
    max_ems_desc = '¡Hay muchos médicos en la ciudad! ¡Solicita ayuda!',
    player_busy = 'Ocupado',
    player_busy_desc = 'Actualmente estás demasiado ocupado para realizar esta acción.',
    cloakroom = 'Sala de Cambio',
    civilian_wear = 'Ropa Civil',
    bill_patient = 'Facturar Paciente',
    bill_patient_desc = 'Envía una factura a un paciente cercano',
    invalid_entry = 'Entrada Inválida',
    invalid_entry_desc = '¡Tu entrada fue inválida!',
    medical_services = 'Servicios Médicos',
    hospital = 'Hospital',
    interact_stretcher_ui = '[E] - Interactuar',
    stretcher_menu_title = 'Interacciones con Camilla',
    open_shop_ui = '[E] - Abrir Farmacia'

}

UIStrings = {
    player_dying = "TE ESTÁS MURIENDO",
    player_passed = "HAS FALLECIDO",
    ems_on_the_way = "¡Los servicios de emergencia están en camino!",
    press_ems_services = "para Servicios de Emergencia",
    press_for_light = "para ver la luz",
    hold = "Sostener",
    time_to_respawn = "Tiempo restante hasta reaparecer",
    press = "Presione",
    player_hurt_critical = "¡Condición Crítica!",
    player_hurt_severe = "Estás gravemente herido",
    player_hurt_unconscious = "Inconsciente",
    player_hurt_unconscious_direct = "Estás inconsciente",
    player_hurt_find_help_or_ems = "Presiona <span class='color'>G</span> para solicitar servicios de emergencia",
    player_hurt_time_to_live = "Desangrándose",
    player_hurt_auto_respawn = "Signos vitales desvaneciéndose",
    player_hurt_respawn = "Presiona E para ver la luz",

    ems_online = "ASISTENCIA EN LÍNEA",
    ems_offline = "ASISTENCIA NO DISPONIBLE",
    currently_online = "ACTUALMENTE EN LÍNEA: "
}
