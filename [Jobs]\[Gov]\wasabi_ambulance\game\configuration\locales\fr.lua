-----------------For support, scripts, and more----------------
--------------- https://discord.gg/wasabiscripts  -------------
---------------------------------------------------------------
if Config.Language ~= 'fr' then return end

Strings = {
    no_society_account = '[^3AVERTISSEMENT^0] Le compte de la société n\'existe pas pour: %s',
    ems_worker = 'Travailleur ambulancier',
    not_authorized = 'Non autorisé',
    not_authorized_desc = 'Vous n\'êtes pas autorisé à utiliser ceci!',
    not_on_duty = 'Pas en service',
    not_on_duty_desc = 'Vous n\'êtes pas en service!',
    gps_enabled = 'GPS activé',
    gps_enabled_desc = 'Vous avez activé votre GPS',
    gps_disabled = 'GPS désactivé',
    gps_disabled_desc = 'Vous avez désactivé votre GPS',

    possible_cause = 'Cause possible',

    full_name = 'Nom',
    pulse = 'Pouls',
    bpm = 'BPM: %s',
    bleed_rate = 'Taux de saignement',
    bleed_rate_low = 'Faible',
    bleed_rate_medium = 'Moyen',
    bleed_rate_high = 'Élevé',
    no_name = 'Pas de nom',
    injuries = 'Blessures actuelles',


    get_off_stretcher_ui = '[E] - Descendre du brancard',
    move = 'Déplacer',
    move_desc = 'Déplacer le brancard',
    put_in_vehicle = 'Mettre dans le véhicule',
    stretcher_in_vehicle = 'Déposer le brancard',
    put_in_vehicle_desc = 'Mettre le brancard dans un véhicule',
    place_on_stretcher = 'Placer le patient',
    place_on_stretcher_desc = 'Placer un patient sur le brancard',
    remove_from_stretcher = 'Retirer le patient',
    remove_from_stretcher_desc = 'Retirer un patient du brancard',
    not_found = 'Non trouvé',
    already_occupied_ambulance = 'Déjà occupé',
    already_occupied_ambulance_desc = 'Cette ambulance est déjà occupée !',
    already_occupied_stretcher = 'Déjà occupé',
    already_occupied_stretcher_desc = 'Ce brancard est déjà occupé !',
    vehicle_occupied = 'Véhicule occupé',
    vehicle_occupied_desc = 'On ne peut pas faire cela avec quelqu’un au volant !',
    not_occupied_stretcher = 'Non occupé',
    not_occupied_stretcher_desc = 'Ce brancard n’est pas occupé !',
    stretcher_placement_blocked = 'Placement bloqué',
    stretcher_placement_blocked_desc = 'Vous ne pouvez pas placer le brancard ici !',
    knocked_out = 'Mis K.O.',
    knocked_out_desc = 'Tu as été mis K.O. !',
    checkin_cooldown = 'Refroidissement de l’enregistrement',
    checkin_cooldown_desc = 'Vous essayez de vous enregistrer trop rapidement, veuillez attendre un moment.',
    checkingin_progress = 'Enregistrement en cours...',
    remove_dead_target = 'Enlever la personne inconsciente',
    ply_injury_head = 'tête',
    ply_injury_neck = 'cou',
    ply_injury_spine = 'colonne vertébrale',
    ply_injury_upper = 'haut du corps',
    ply_injury_lower = 'bas du corps',
    ply_injury_left_arm = 'bras gauche',
    ply_injury_left_leg = 'jambe gauche',
    ply_injury_right_arm = 'bras droit',
    ply_injury_right_leg = 'jambe droite',
    diagnosing_patient_progress = 'Diagnostic du patient en cours...',
    treating_patient_progress = 'Traitement du patient en cours...',
    recovering_progress = 'Récupération en cours...',
    injury_report = 'Rapport de blessure',
    none = 'Aucun',
    mild = 'Léger',
    moderate = 'Modéré',
    severe = 'Grave',
    deadly = 'Mortel',
    injury_report_locations = 'Localisation',
    injury_report_type = 'Type de blessure : %s',
    injury_report_severity = 'Gravité : %s',
    injury_report_bleed = 'Perte de sang',
    light_injury_title = 'Blessure légère',
    moderate_injury_title = 'Blessure modérée',
    extreme_injury_title = 'Blessure extrême',
    injury_bleed_notify = 'Alerte Blessure et Saignement',
    light_injury_desc = 'Votre %s est blessé, envisagez de consulter un médecin !',
    moderate_injury_desc = 'Votre %s est très blessé, vous avez besoin d’un médecin !',
    extreme_injury_desc = 'Votre %s est extrêmement blessé. Vous devez voir un médecin avant qu’il ne soit trop tard !',
    injury_msg_one = 'Vous avez une blessure importante.',
    injury_msg_two = 'Vous avez une blessure sérieuse.',
    injury_msg_three = 'Vous avez une blessure sévère.',
    injury_msg_four = 'Vous avez une blessure critique.',
    bleed_msg_one = 'Vous saignez.',
    bleed_msg_two = 'Vous saignez abondamment. Appliquez de la pression.',
    bleed_msg_three = 'Vous allez vous vider de votre sang !',
    fainted_title = 'Évanoui',
    fainted_so_high_desc = 'Vous vous êtes évanoui à cause de la hauteur.',
    cant_jump_title = 'Impossible de sauter !',
    cant_jump_desc = 'Vous êtes trop blessé pour tenter de sauter',
    blackout_title = 'Perte de conscience',
    blackout_desc =
    'Vous avez perdu connaissance à cause de votre blessure %s ! Cherchez immédiatement une attention médicale !',
    treated_fully_desc = 'Vous avez été traité et vous vous sentez mieux que jamais !',
    treated_not_fully_desc = 'Vous avez été traité mais avez besoin de soins supplémentaires.',
    prescription_menu = 'Menu des prescriptions',
    prescription_menu_desc = 'Accéder et gérer les prescriptions',
    no_stretcher_detected = 'Aucune civière détectée',
    no_stretcher_detected_desc = 'Aucune civière active détectée !',
    cant_run = 'Impossible de sprinter',
    cant_run_desc = 'Vous êtes trop blessé pour sprinter !',
    no_back_seat = 'Pas de sièges disponibles',
    no_back_seat_desc = 'Il n’y a pas de sièges à l’arrière de cette ambulance',
    enter_vehicle_back = 'Entrer dans l’ambulance (Arrière)',
    stretcher_already_deployed = 'Civière déjà déployée',
    stretcher_already_deployed_desc = 'La civière attribuée à cette ambulance a déjà été retirée.',
    send_stretcher_home = 'Retourner au véhicule',
    ambulance_not_found = 'La civière a été retirée mais l’ambulance n’a pas été trouvée pour revenir !',
    bleedout = 'saignement',
    player_injury_critical_desc = 'Le joueur est dans un état critique en raison d’une blessure %s !',
    gps_active = 'GPS Activé',
    gps_active_desc = 'Le joueur %s a activé son GPS',
    gps_deactive = 'GPS Désactivé',
    gps_deactive_desc = 'Le joueur %s a désactivé son GPS',
    no_wsb = '^0[^3AVERTISSEMENT^0] wasabi_bridge n’a PAS été démarré APRÈS le cadre et/ou AVANT la ressource : %s',
    spawn_blocked = 'Garage bloqué',
    spawn_blocked_desc = 'Vous ne pouvez pas sortir votre véhicule car il est bloqué !',
    menu_remove_crutch = 'Enlever la béquille',
    menu_remove_crutch_desc = 'Enlever une béquille à un patient proche',
    menu_remove_chair = 'Enlever Fauteuil Roulant',
    menu_remove_chair_desc = 'Enlever un fauteuil roulant à un patient proche',
    toggle_stretcher = 'Prendre civière',
    toggle_stretcher_desc = 'Prenez la civière du véhicule le plus proche',
    no_vehicle_nearby = 'Véhicule',
    no_vehicle_nearby_desc = 'Il n\'y a pas de véhicule à proximité',
    no_ambulance_nearby_desc = 'Il n\'y a pas d\'ambulance à proximité',
    on_duty = 'En Service',
    on_duty_desc = 'Vous venez de passer en service !',
    off_duty = 'Hors Service',
    off_duty_desc = 'Vous venez de passer hors service !',
    amount = 'Montant',
    mr = 'M.',
    mrs = 'Mme',
    debt_collection = 'Recouvrement de Dettes',
    db_email =
    'Cher/Chère %s %s, <br /><br />L\'Agence Centrale de Recouvrement Judiciaire (CJCA) a chargé les amendes que vous avez reçues de la police.<br />Il y a <strong>$%s</strong> prélevés de votre compte.<br /><br />Cordialement,<br />M. Wasabi',
    fine_sent = 'Amendes Envoyées',
    fine_sent_desc = 'Vous avez envoyé avec succès une amende de $%s !',
    fine_received = 'Amende Reçue',
    fine_received_desc = 'Vous avez reçu une amende de $%s',
    log_killed_title = 'Joueur Tué',
    log_killed_desc =
    '> *%s [%s] a tué %s [%s]\n\n**Informations sur le Tueur :**\nNom : `%s`\nID Joueur : `%s`\nIdentifiant : `%s`\nSteam : `%s`\nDiscord : `%s`\nLicence : `%s`\nLicence2 : `%s`\nXBL : `%s`\nFiveM : `%s`\nIP : ||%s||\n\n**Informations sur la Victime :**\nNom du Personnage : `%s`\nID Joueur : `%s`\nIdentifiant : `%s`\nSteam : `%s`\nDiscord : `%s`\nLicence : `%s`\nLicence2 : `%s`\nXBL : `%s`\nFiveM : `%s`\nIP : ||%s||\n\n> **Cause de la Mort :** `%s`',
    log_suicide_title = 'Joueur s\'est Suicidé',
    log_suicide_desc =
    '> %s [%s] s\'est suicidé\n\n**Informations sur le Joueur :**\nNom : `%s`\nID Joueur : `%s`\nIdentifiant : `%s`\nSteam : `%s`\nDiscord : `%s`\nLicence : `%s`\nLicence2 : `%s`\nXBL : `%s`\nFiveM : `%s`\nIP : ||%s||\n\n> **Cause de la Mort :** `%s`',
    unknown = 'INCONNU',
    log_admin_revive_title = 'Joueur Réanimé par l\'Admin',
    log_admin_revive_desc =
    '> *%s [%s] a été réanimé par l\'admin %s [%s]\n\n**Informations sur la Cible :**\nNom : `%s`\nID Joueur : `%s`\nIdentifiant : `%s`\nSteam : `%s`\nDiscord : `%s`\nLicence : `%s`\nLicence2 : `%s`\nXBL : `%s`\nFiveM : `%s`\nIP : ||%s||\n\n**Informations sur l\'Admin :**\nNom du Personnage : `%s`\nID Joueur : `%s`\nIdentifiant : `%s`\nSteam : `%s`\nDiscord : `%s`\nLicence : `%s`\nLicence2 : `%s`\nXBL : `%s`\nFiveM : `%s`\nIP : ||%s||',
    log_self_revive_title = 'Auto-Réanimation de l\'Admin',
    log_self_revive_desc =
    '> %s [%s] s\'est auto-réanimé\n\n**Informations sur le Joueur :**\nNom : `%s`\nID Joueur : `%s`\nIdentifiant : `%s`\nSteam : `%s`\nDiscord : `%s`\nLicence : `%s`\nLicence2 : `%s`\nXBL : `%s`\nFiveM : `%s`\nIP : ||%s||',
    log_death_logs = 'Logs de Décès',
    log_revive_logs = 'Logs de Réanimation',
    log_combat_logs = 'Journaux de Combat',
    log_combatlog_title = 'Journal de Combat',
    log_combatlog_desc =
    '> %s [%s] journal de combat \n\n**Informations sur le joueur:**\nNom: `%s`\nID joueur: `%s`\nIdentifiant: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicence: `%s`\nLicence2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    medbag_crutch = 'Béquille',
    medbag_crutch_desc = 'Une béquille pour aider les patients à marcher',
    menu_crutch = 'Appliquer Béquille',
    menu_crutch_desc = 'Ajouter une béquille à un patient proche',
    medbag_chair = 'Fauteuil Roulant',
    medbag_chair_desc = 'Un fauteuil roulant pour aider les patients qui ne peuvent pas marcher',
    menu_chair = 'Appliquer Fauteuil Roulant',
    menu_chair_desc = 'Ajouter un fauteuil roulant à un patient proche',
    shot = 'blessure par balle',
    stabbed = 'coup de couteau',
    beat = 'traumatisme contondant',
    burned = 'brûlure',
    other = 'inconnu',
    JobMenuTitle = 'Menu EMS',
    dispatch = 'Expédition',
    dispatch_desc = 'Vérifier les joueurs ayant besoin d\'aide',
    DispatchMenuTitle = 'Expédition',
    GoBack = 'Retour',
    key_map_text = 'Menu du Travail d\'Ambulance',
    assistance_title = 'Demande d\'Assistance',
    assistance_desc = 'Une personne a demandé une assistance médicale !',
    respawn_available_in = 'Réapparition disponible dans ~r~%s minutes %s secondes~s~\n',
    respawn_bleedout_in = 'Vous allez saigner à mort dans ~r~%s minutes %s secondes~s~\n',
    respawn_bleedout_prompt = 'Maintenez [~r~E~s~] pour réapparaître',
    distress_send = 'Appuyez sur [~r~G~s~] pour envoyer un signal de détresse à l\'EMS',
    distress_sent_title = 'Demande d\'Assistance',
    distress_sent_desc = 'Le signal de détresse a été envoyé aux unités disponibles !',
    route_set_title = 'Itinéraire Défini',
    route_set_desc = 'Un point de cheminement a été défini pour la personne en détresse !',
    diagnose_patient = 'Diagnostiquer',
    diagnose_patient_desc = 'Diagnostiquer la personne blessée la plus proche',
    no_requests = 'Aucune demande active',
    revive_patient = 'Réanimer',
    revive_patient_desc = 'Tenter de réanimer une personne proche',
    heal_patient = 'Soigner',
    heal_patient_desc = 'Tenter de soigner une personne proche',
    sedate_patient = 'Sédater',
    sedate_patient_desc = 'Sédater temporairement une personne proche',
    drag_patient = 'Traîner',
    drag_patient_desc = 'Traîner une personne blessée proche',
    place_stretcher_target = 'Ajouter ou Retirer Patient',
    place_patient = 'Placer dans/sortir du Véhicule',
    place_patient_desc = 'Placer une personne inconsciente proche dans/sortir du véhicule',
    no_nearby = 'Non trouvé',
    no_nearby_desc = 'Il semble qu\'il n\'y ait personne autour',
    no_injury = 'Pas de blessure',
    no_injury_desc = 'Personne ne semble avoir besoin d\'aucun traitement',
    no_injury_dead_desc =
    'Le patient ne semble pas avoir besoin de traitement supplémentaire. Essayez d\'utiliser le défibrillateur et espérez le meilleur !',
    player_injury = 'Personne blessée',
    player_injury_desc = 'Cette personne semble avoir une blessure de %s',
    player_not_unconscious = 'Joueur conscient',
    player_not_unconscious_desc = 'Il semble que le joueur soit conscient',
    player_unconscious = 'Joueur inconscient',
    player_unconscious_desc = 'Le patient doit être conscient pour ce traitement !',
    player_reviving = 'Réanimation',
    player_reviving_desc = 'Réanimation du patient en cours',
    player_noitem = 'Élément manquant',
    player_noitem_desc = 'Vous n\'avez pas l\'élément requis pour cela !',
    player_successful_revive = 'Réanimé',
    player_successful_revive_reward_desc = 'Vous avez réussi à réanimer le patient et à gagner %s !',
    player_successful_revive_desc = 'Vous avez réussi à réanimer le patient !',
    player_healing = 'Guérison',
    player_healing_desc = 'Guérison du patient en cours',
    player_successful_heal = 'Guéri',
    player_successful_heal_desc = 'Patient guéri avec succès !',
    player_healed_desc = 'Vous avez été guéri avec succès par le médecin !',
    not_medic = 'Non autorisé',
    not_medic_desc = 'Vous n\'êtes pas formé pour utiliser cela !',
    target_sedated = 'Sédaté',
    target_sedated_desc = 'Vous avez été sédaté par un professionnel de la santé',
    player_successful_sedate_desc = 'Vous avez réussi à sédaté le patient',
    drop_bag_ui = '[E] - Déposer le sac',
    drop_stretch_ui = '[E] - Placer la civière',
    pickup_bag_target = 'Ramasser',
    move_target = 'Déplacer',
    interact_bag_target = 'Ouvrir',
    successful = 'Succès',
    medbag_pickup = 'Vous avez ramassé le sac médical',
    medbag_pickup_civ = 'Vous avez fouillé le sac et pris ce qui serait utile',
    stretcher_pickup = 'Vous avez renvoyé la civière à l\'ambulance d\'où elle venait',
    medbag = 'Sac médical',
    medbag_tweezers = 'Pince à épiler',
    medbag_tweezers_desc = 'Utilisée pour enlever les balles',
    medbag_suture = 'Kit de suture',
    medbag_suture_desc = 'Utilisé pour recoudre les blessures',
    medbag_icepack = 'Compresses froides',
    medbag_icepack_desc = 'Utilisées pour réduire le gonflement',
    medbag_burncream = 'Crème contre les brûlures',
    medbag_burncream_desc = 'Utilisée pour traiter les brûlures',
    medbag_defib = 'Défibrillateur',
    medbag_defib_desc = 'Pour réanimer les patients',
    medbag_medikit = 'Trousse de secours',
    medbag_medikit_desc = 'Utilisée pour soigner les patients',
    medbag_sedative = 'Sédatif',
    medbag_sedative_desc = 'Utilisé pour sédaté les patients',
    medbag_stretcher = 'Civière pliante',
    medbag_stretcher_desc = 'Utilisée pour déplacer les patients',
    item_grab = 'Vous avez sorti un outil de votre sac médical',
    wrong_equipment = 'Mauvais équipement !',
    wrong_equipment_desc = 'Avez-vous même été formé ?',
    player_not_injured = 'Pas de blessure',
    player_not_injured_desc =
    'Cette personne ne semble pas avoir besoin de traitement supplémentaire et est claire pour le défibrillateur',
    player_treated = 'Traitée',
    player_treated_desc = 'Vous avez réussi à traiter le patient',
    revive_command_help = 'Une commande d\'administrateur pour réanimer les joueurs',
    revive_command_arg = "L'ID du joueur",
    reviveall_command_help = 'Une commande d\'administrateur pour réanimer tous les joueurs',
    revivearea_command_help = 'Une commande d\'administrateur pour réanimer les joueurs à proximité',
    revivearea_command_arg = "Zone pour réanimer les joueurs",
    resetdeathcount_command_help = "Une commande d'administrateur pour réinitialiser le compte de décès des joueurs",
    resetdeathcount_command_arg = "L'ID du joueur",
    viewdeathcount_command_help = 'Une commande pour voir votre compte de décès',
    alive_again = 'En vie',
    alive_again_desc = 'Vous avez été déposé par un habitant à l\'hôpital !',
    request_supplies_target = 'Fournitures médicales',
    currency = '$',
    not_enough_funds = 'Fonds insuffisants',
    not_enough_funds_desc = 'Vous n\'avez pas assez de fonds !',
    hospital_garage = 'Garage de l\'hôpital',
    used_meditkit = 'Trousse de soins utilisée',
    used_medikit_desc = 'Vous avez réussi à vous soigner',
    action_cancelled = 'Action annulée',
    action_cancelled_desc = 'Vous avez annulé votre dernière action !',
    healing_self_prog = 'Traitement des blessures en cours',
    checkin_hospital = 'Succès',
    checkin_hospital_desc = 'Vous avez été traité avec succès par le personnel hospitalier',
    max_ems = 'Médecins disponibles',
    max_ems_desc = 'Il y a beaucoup de médecins en ville ! Demandez de l\'aide !',
    player_busy = 'Occupé',
    player_busy_desc = 'Vous êtes actuellement trop occupé pour effectuer cette action !',
    cloakroom = 'Vestiaire',
    civilian_wear = 'Tenue civile',
    bill_patient = 'Facturer le patient',
    bill_patient_desc = 'Envoyez une facture à un patient à proximité',
    invalid_entry = 'Invalide',
    invalid_entry_desc = 'Votre saisie était invalide !',
    medical_services = 'Services médicaux',
    hospital = 'Hôpital',
    interact_stretcher_ui = '[E] - Interagir',
    stretcher_menu_title = 'Interactions avec la civière',
    open_shop_ui = '[E] - Ouvrir la pharmacie'

}

UIStrings = {
    player_dying = "VOUS ÊTES EN TRAIN DE MOURIR",
    player_passed = "VOUS ÊTES DÉCÉDÉ",
    ems_on_the_way = "Les services d'urgence sont en route!",
    press_ems_services = "pour les services d'urgence",
    press_for_light = "pour voir la lumière",
    hold = "Tenir",
    time_to_respawn = "Temps restant avant réapparition",
    press = "Appuyez",
    player_hurt_critical = "État critique!",
    player_hurt_severe = "Vous êtes gravement blessé",
    player_hurt_unconscious = "Inconscient",
    player_hurt_unconscious_direct = "Vous êtes inconscient",
    player_hurt_find_help_or_ems = "Appuyez sur <span class='color'>G</span> pour demander des services d'urgence",
    player_hurt_time_to_live = "En train de saigner",
    player_hurt_auto_respawn = "Signes vitaux en baisse",
    player_hurt_respawn = "Appuyez sur E pour voir la lumière",

    ems_online = "ASSISTANCE EN LIGNE",
    ems_offline = "ASSISTANCE NON DISPONIBLE",
    currently_online = "ACTUELLEMENT EN LIGNE: "
}
