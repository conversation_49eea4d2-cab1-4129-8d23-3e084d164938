-----------------For support, scripts, and more----------------
--------------- https://discord.gg/wasabiscripts  -------------
---------------------------------------------------------------
if Config.Language ~= 'it' then return end

Strings = {
    no_society_account = '[^3AVVISO^0] Il conto della società non esiste per: %s',
    ems_worker = 'Operatore dell\'ambulanza',
    not_authorized = 'Non autorizzato',
    not_authorized_desc = 'Non sei autorizzato a utilizzare questo!',
    not_on_duty = 'Non in servizio',
    not_on_duty_desc = 'Non sei in servizio!',
    gps_enabled = 'GPS abilitato',
    gps_enabled_desc = 'Hai abilitato il tuo GPS',
    gps_disabled = 'GPS disabilitato',
    gps_disabled_desc = 'Hai disabilitato il tuo GPS',

    possible_cause = 'Causa possibile',

    full_name = 'Nome',
    pulse = 'Polso',
    bpm = 'BPM: %s',
    bleed_rate = 'Tasso di sanguinamento',
    bleed_rate_low = 'Basso',
    bleed_rate_medium = 'Medio',
    bleed_rate_high = 'Alto',
    no_name = 'Nessun nome',
    injuries = 'Infortuni attuali',


    get_off_stretcher_ui = '[E] - Scendi dalla barella',
    move = 'Muovi',
    move_desc = 'Muovi la barella',
    put_in_vehicle = 'Metti nel veicolo',
    stretcher_in_vehicle = 'Deposita Barella',
    put_in_vehicle_desc = 'Metti la barella in un veicolo',
    place_on_stretcher = 'Posiziona Paziente',
    place_on_stretcher_desc = 'Posiziona un paziente sulla barella',
    remove_from_stretcher = 'Rimuovi Paziente',
    remove_from_stretcher_desc = 'Rimuovi un paziente dalla barella',
    not_found = 'Non Trovato',
    already_occupied_ambulance = 'Già Occupata',
    already_occupied_ambulance_desc = 'Questa ambulanza è già occupata!',
    already_occupied_stretcher = 'Già Occupata',
    already_occupied_stretcher_desc = 'Questa barella è già occupata!',
    vehicle_occupied = 'Veicolo Occupato',
    vehicle_occupied_desc = 'Non si può fare questo mentre qualcuno sta guidando!',
    not_occupied_stretcher = 'Non Occupata',
    not_occupied_stretcher_desc = 'Questa barella non è occupata!',
    stretcher_placement_blocked = 'Posizionamento Bloccato',
    stretcher_placement_blocked_desc = 'Non puoi posizionare la barella qui!',
    knocked_out = 'Knockout',
    knocked_out_desc = 'Sei stato messo KO!',
    checkin_cooldown = 'Tempo di Attesa per Check-in',
    checkin_cooldown_desc = 'Stai tentando di fare il check-in troppo velocemente, attendi un momento.',
    checkingin_progress = 'Check-in in corso...',
    remove_dead_target = 'Rimuovi Persona Incosciente',
    ply_injury_head = 'testa',
    ply_injury_neck = 'collo',
    ply_injury_spine = 'spina dorsale',
    ply_injury_upper = 'parte superiore del corpo',
    ply_injury_lower = 'parte inferiore del corpo',
    ply_injury_left_arm = 'braccio sinistro',
    ply_injury_left_leg = 'gamba sinistra',
    ply_injury_right_arm = 'braccio destro',
    ply_injury_right_leg = 'gamba destra',
    diagnosing_patient_progress = 'Diagnosi del paziente in corso...',
    treating_patient_progress = 'Trattamento del paziente in corso...',
    recovering_progress = 'Recupero in corso...',
    injury_report = 'Report delle Lesioni',
    none = 'Nessuno',
    mild = 'Leggera',
    moderate = 'Moderata',
    severe = 'Grave',
    deadly = 'Mortale',
    injury_report_locations = 'Localizzazione',
    injury_report_type = 'Tipo di Lesione: %s',
    injury_report_severity = 'Gravità: %s',
    injury_report_bleed = 'Perdita di Sangue',
    light_injury_title = 'Infortunio Leggero',
    moderate_injury_title = 'Infortunio Moderato',
    extreme_injury_title = 'Infortunio Grave',
    injury_bleed_notify = 'Avviso di Lesione e Sanguinamento',
    light_injury_desc = 'Il tuo %s è ferito, considera di visitare un medico!',
    moderate_injury_desc = 'Il tuo %s è molto ferito, hai bisogno di un medico!',
    extreme_injury_desc = 'Il tuo %s è estremamente ferito. Devi andare dal medico prima che sia troppo tardi!',
    injury_msg_one = 'Hai una lesione significativa.',
    injury_msg_two = 'Hai una lesione grave.',
    injury_msg_three = 'Hai una lesione molto grave.',
    injury_msg_four = 'Hai una lesione critica.',
    bleed_msg_one = 'Stai sanguinando.',
    bleed_msg_two = 'Stai sanguinando abbondantemente. Applica pressione.',
    bleed_msg_three = 'Stai per dissanguarti!',
    fainted_title = 'Svenuto',
    fainted_so_high_desc = 'Sei svenuto per essere troppo in alto.',
    cant_jump_title = 'Non Puoi Saltare!',
    cant_jump_desc = 'Sei troppo ferito per tentare di saltare',
    blackout_title = 'Perdita di Coscienza',
    blackout_desc = 'Hai perso conoscenza a causa della tua lesione a %s! Cerca immediatamente assistenza medica!',
    treated_fully_desc = 'Sei stato trattato e ti senti meglio che mai!',
    treated_not_fully_desc = 'Sei stato trattato, ma hai bisogno di ulteriori cure.',
    prescription_menu = 'Menu Prescrizioni',
    prescription_menu_desc = 'Accedi e gestisci le prescrizioni',
    no_stretcher_detected = 'Nessuna Barella Rilevata',
    no_stretcher_detected_desc = 'Non è stata rilevata nessuna barella attiva!',
    cant_run = 'Non Puoi Correre',
    cant_run_desc = 'Sei troppo ferito per correre!',
    no_back_seat = 'Nessun Posto Disponibile',
    no_back_seat_desc = 'Non ci sono posti nel retro di quest\'ambulanza',
    enter_vehicle_back = 'Entra nell\'Ambulanza (Retro)',
    stretcher_already_deployed = 'Barella Già Dispiegata',
    stretcher_already_deployed_desc = 'La barella assegnata a quest\'ambulanza è già stata rimossa.',
    send_stretcher_home = 'Ritorna al Veicolo',
    ambulance_not_found = 'La barella è stata rimossa ma l\'ambulanza non è stata trovata per tornare!',
    bleedout = 'emorragia',
    player_injury_critical_desc = 'Il giocatore è in condizioni critiche a causa di una ferita %s!',
    gps_active = 'GPS Attivato',
    gps_active_desc = 'Il giocatore %s ha attivato il suo GPS',
    gps_deactive = 'GPS Disattivato',
    gps_deactive_desc = 'Il giocatore %s ha disattivato il suo GPS',
    no_wsb = '^0[^3AVVERTIMENTO^0] wasabi_bridge non è stato avviato DOPO il framework e/o PRIMA della risorsa: %s',
    spawn_blocked = 'Garage Bloccato',
    spawn_blocked_desc = 'Non puoi tirare fuori il tuo veicolo perché è bloccato!',
    menu_remove_crutch = 'Rimuovi Stampella',
    menu_remove_crutch_desc = 'Rimuovere una stampella da un paziente vicino',
    menu_remove_chair = 'Rimuovere Sedia a Rotelle',
    menu_remove_chair_desc = 'Rimuovere una sedia a rotelle da un paziente vicino',
    toggle_stretcher = 'Prendere barella',
    toggle_stretcher_desc = 'Prendere la barella dal veicolo più vicino',
    no_vehicle_nearby = 'Veicolo',
    no_vehicle_nearby_desc = 'Non ci sono veicoli nelle vicinanze',
    no_ambulance_nearby_desc = 'Non ci sono ambulanze nelle vicinanze',
    on_duty = 'In Servizio',
    on_duty_desc = 'Sei appena passato in servizio!',
    off_duty = 'Fuori Servizio',
    off_duty_desc = 'Sei appena passato fuori servizio!',
    amount = 'Importo',
    mr = 'Sig.',
    mrs = 'Sig.ra',
    debt_collection = 'Recupero Debiti',
    db_email =
    'Gentile %s %s, <br /><br />L\'Agenzia Centrale di Recupero Giudiziario (CJCA) ha addebitato le multe che hai ricevuto dalla polizia.<br />Sono stati prelevati <strong>$%s</strong> dal tuo conto.<br /><br />Cordiali saluti,<br />Sig. Wasabi',
    fine_sent = 'Multe Inviata',
    fine_sent_desc = 'Hai inviato con successo una multa di $%s!',
    fine_received = 'Multa Ricevuta',
    fine_received_desc = 'Hai ricevuto una multa di $%s',
    log_killed_title = 'Giocatore Ucciso',
    log_killed_desc =
    '> *%s [%s] ha ucciso %s [%s]\n\n**Informazioni sull\'Assassino:**\nNome: `%s`\nID Giocatore: `%s`\nIdentificativo: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicenza: `%s`\nLicenza2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n**Informazioni sulla Vittima:**\nNome Personaggio: `%s`\nID Giocatore: `%s`\nIdentificativo: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicenza: `%s`\nLicenza2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n> **Causa della Morte:** `%s`',
    log_suicide_title = 'Giocatore Si è Suicidato',
    log_suicide_desc =
    '> %s [%s] si è suicidato\n\n**Informazioni sul Giocatore:**\nNome: `%s`\nID Giocatore: `%s`\nIdentificativo: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicenza: `%s`\nLicenza2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n> **Causa della Morte:** `%s`',
    unknown = 'SCONOSCIUTO',
    log_admin_revive_title = 'Giocatore Rianimato dall\'Amministratore',
    log_admin_revive_desc =
    '> *%s [%s] è stato rianimato dall\'amministratore %s [%s]\n\n**Informazioni sul Bersaglio:**\nNome: `%s`\nID Giocatore: `%s`\nIdentificativo: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicenza: `%s`\nLicenza2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n**Informazioni sull\'Amministratore:**\nNome Personaggio: `%s`\nID Giocatore: `%s`\nIdentificativo: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicenza: `%s`\nLicenza2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    log_self_revive_title = 'Auto-Rianimazione dell\'Amministratore',
    log_self_revive_desc =
    '> %s [%s] si è auto-rianimato\n\n**Informazioni sul Giocatore:**\nNome: `%s`\nID Giocatore: `%s`\nIdentificativo: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicenza: `%s`\nLicenza2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    log_death_logs = 'Registri di Morte',
    log_revive_logs = 'Registri di Rianimazione',
    log_combat_logs = 'Registri di Combattimento',
    log_combatlog_title = 'Registro di Combattimento',
    log_combatlog_desc =
    '> %s [%s] registro di combattimento \n\n**Informazioni sul giocatore:**\nNome: `%s`\nID giocatore: `%s`\nIdentificatore: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicenza: `%s`\nLicenza2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    medbag_crutch = 'Stampella',
    medbag_crutch_desc = 'Una stampella per assistere i pazienti nel camminare',
    menu_crutch = 'Applica Stampella',
    menu_crutch_desc = 'Aggiungi una stampella a un paziente vicino',
    medbag_chair = 'Sedia a Rotelle',
    medbag_chair_desc = 'Una sedia a rotelle per assistere i pazienti che non possono camminare',
    menu_chair = 'Applica Sedia a Rotelle',
    menu_chair_desc = 'Aggiungi una sedia a rotelle a un paziente vicino',
    shot = 'colpo d\'arma da fuoco',
    stabbed = 'pugnalata',
    beat = 'trauma da forza contundente',
    burned = 'ustione',
    other = 'sconosciuto',
    JobMenuTitle = 'Menu EMS',
    dispatch = 'Spedizione',
    dispatch_desc = 'Controlla i giocatori che hanno bisogno di assistenza',
    DispatchMenuTitle = 'Spedizione',
    GoBack = 'Indietro',
    key_map_text = 'Menu Lavoro Ambulanza',
    assistance_title = 'Assistenza Richiesta',
    assistance_desc = 'Una persona ha richiesto assistenza medica!',
    respawn_available_in = 'Reincarnazione disponibile in ~r~%s minuti %s secondi~s~\n',
    respawn_bleedout_in = 'Sanguinerai fino alla morte in ~r~%s minuti %s secondi~s~\n',
    respawn_bleedout_prompt = 'Tieni premuto [~r~E~s~] per reincarnarti',
    distress_send = 'Premi [~r~G~s~] per inviare un segnale di soccorso all\'EMS',
    distress_sent_title = 'Assistenza Richiesta',
    distress_sent_desc = 'Il segnale di soccorso è stato inviato alle unità disponibili!',
    route_set_title = 'Percorso Impostato',
    route_set_desc = 'È stato impostato un punto di via alla persona in difficoltà!',
    diagnose_patient = 'Diagnostica',
    diagnose_patient_desc = 'Diagnostica la persona ferita più vicina',
    no_requests = 'Nessuna richiesta attiva',
    revive_patient = 'Rianima',
    revive_patient_desc = 'Tenta di rianimare una persona vicina',
    heal_patient = 'Guarisci',
    heal_patient_desc = 'Tenta di guarire una persona vicina',
    sedate_patient = 'Sedare',
    sedate_patient_desc = 'Sedare temporaneamente una persona vicina',
    drag_patient = 'Trascina',
    drag_patient_desc = 'Trascina una persona ferita vicina',
    place_stretcher_target = 'Aggiungi o Rimuovi Paziente',
    place_patient = 'Posiziona dentro/fuori dal Veicolo',
    place_patient_desc = 'Posiziona una persona incosciente vicina dentro/fuori dal veicolo',
    no_nearby = 'Non trovato',
    no_nearby_desc = 'Sembra che non ci sia nessuno in giro',
    no_injury = 'Nessuna ferita',
    no_injury_desc = 'La persona sembra non aver bisogno di alcun trattamento',
    no_injury_dead_desc =
    'Il paziente sembra non aver bisogno di ulteriori cure. Prova a usare il defibrillatore e spera nel meglio!',
    player_injury = 'Persona ferita',
    player_injury_desc = 'Questa persona sembra avere una ferita %s',
    player_not_unconscious = 'Giocatore cosciente',
    player_not_unconscious_desc = 'Sembra che il giocatore sia cosciente',
    player_unconscious = 'Giocatore svenuto',
    player_unconscious_desc = 'Il paziente deve essere cosciente per questo trattamento!',
    player_reviving = 'Rianimazione',
    player_reviving_desc = 'In corso la rianimazione del paziente',
    player_noitem = 'Oggetto mancante',
    player_noitem_desc = 'Ti manca l\'oggetto richiesto per questo!',
    player_successful_revive = 'Rianimato',
    player_successful_revive_reward_desc = 'Hai rianimato con successo il paziente e guadagnato $%s!',
    player_successful_revive_desc = 'Hai rianimato con successo il paziente!',
    player_healing = 'Guarigione',
    player_healing_desc = 'In corso la guarigione del paziente',
    player_successful_heal = 'Guarito',
    player_successful_heal_desc = 'Paziente guarito con successo!',
    player_healed_desc = 'Sei stato guarito con successo dal medico!',
    not_medic = 'Non autorizzato',
    not_medic_desc = 'Non sei addestrato per usare questo!',
    target_sedated = 'Sedato',
    target_sedated_desc = 'Sei stato sedato da un professionista medico',
    player_successful_sedate_desc = 'Hai sedato con successo il paziente',
    drop_bag_ui = '[E] - Lascia la borsa',
    drop_stretch_ui = '[E] - Posiziona la barella',
    pickup_bag_target = 'Raccogli',
    move_target = 'Sposta',
    interact_bag_target = 'Apri',
    successful = 'Riuscito',
    medbag_pickup = 'Hai preso la borsa medica',
    medbag_pickup_civ = 'Hai ispezionato la borsa e hai preso ciò che potrebbe essere utile',
    stretcher_pickup = 'Hai mandato la barella all\'ambulanza da cui è stata tirata fuori',
    medbag = 'Borsa medica',
    medbag_tweezers = 'Pinzette',
    medbag_tweezers_desc = 'Usate per rimuovere i proiettili',
    medbag_suture = 'Set di sutura',
    medbag_suture_desc = 'Usato per suturare le ferite',
    medbag_icepack = 'Borsa del ghiaccio',
    medbag_icepack_desc = 'Usato per ridurre il gonfiore',
    medbag_burncream = 'Crema per le ustioni',
    medbag_burncream_desc = 'Usata per trattare le ustioni',
    medbag_defib = 'Defibrillatore',
    medbag_defib_desc = 'Per rianimare i pazienti',
    medbag_medikit = 'Medikit',
    medbag_medikit_desc = 'Usato per curare i pazienti',
    medbag_sedative = 'Sedativo',
    medbag_sedative_desc = 'Usato per sedare i pazienti',
    medbag_stretcher = 'Barellabile pieghevole',
    medbag_stretcher_desc = 'Usato per spostare i pazienti',
    item_grab = 'Hai tirato fuori uno strumento dalla tua borsa medica',
    wrong_equipment = 'Attrezzatura sbagliata!',
    wrong_equipment_desc = 'Sei stato addestrato?',
    player_not_injured = 'Non ferito',
    player_not_injured_desc =
    'Questa persona sembra non aver bisogno di ulteriori trattamenti ed è pronta per il defibrillatore',
    player_treated = 'Trattato',
    player_treated_desc = 'Hai trattato con successo il paziente',
    revive_command_help = 'Un comando amministratore per rianimare i giocatori',
    revive_command_arg = 'ID del giocatore',
    reviveall_command_help = 'Un comando amministratore per rianimare tutti i giocatori',
    revivearea_command_help = 'Un comando amministratore per rianimare i giocatori nelle vicinanze',
    revivearea_command_arg = 'Area per rianimare i giocatori',
    resetdeathcount_command_help = 'Un comando amministratore per reimpostare il conteggio delle morti dei giocatori',
    resetdeathcount_command_arg = 'ID del giocatore',
    viewdeathcount_command_help = 'Un comando per visualizzare il conteggio delle tue morti',
    alive_again = 'Vivo di nuovo',
    alive_again_desc = 'Sei stato lasciato all\'ospedale da un locale!',
    request_supplies_target = 'Forniture mediche',
    currency = '$',
    not_enough_funds = 'Fondi insufficienti',
    not_enough_funds_desc = 'Non hai abbastanza fondi!',
    hospital_garage = 'Garage dell\'ospedale',
    used_meditkit = 'Medikit Usato',
    used_medikit_desc = 'Sei riuscito a medicarti',
    action_cancelled = 'Azione Annullata',
    action_cancelled_desc = 'Hai annullato l\'ultima azione!',
    healing_self_prog = 'Trattamento Ferite',
    checkin_hospital = 'Successo',
    checkin_hospital_desc = 'Sei stato trattato con successo dal personale dell\'ospedale',
    max_ems = 'Paramedici Disponibili',
    max_ems_desc = 'Ci sono molti paramedici in città! Richiedi aiuto!',
    player_busy = 'Occupato',
    player_busy_desc = 'Al momento sei troppo occupato per eseguire questa azione!',
    cloakroom = 'Sala Cambio',
    civilian_wear = 'Abbigliamento Civile',
    bill_patient = 'Fattura Paziente',
    bill_patient_desc = 'Invia una fattura a un paziente nelle vicinanze',
    invalid_entry = 'Non Valido',
    invalid_entry_desc = 'La tua voce non è valida!',
    medical_services = 'Servizi Medici',
    hospital = 'Ospedale',
    interact_stretcher_ui = '[E] - Interagisci',
    stretcher_menu_title = 'Interazioni con la barella',
    open_shop_ui = '[E] - Apri Farmacia'


}

UIStrings = {
    player_dying = "STAI MORENDO",
    player_passed = "SEI PASSATO",
    ems_on_the_way = "I servizi di emergenza sono in arrivo!",
    press_ems_services = "per i servizi di emergenza",
    press_for_light = "per vedere la luce",
    hold = "Tenere",
    time_to_respawn = "Tempo rimanente prima della ricomparsa",
    press = "Premere",
    player_hurt_critical = "Condizione critica!",
    player_hurt_severe = "Sei gravemente ferito",
    player_hurt_unconscious = "Incosciente",
    player_hurt_unconscious_direct = "Sei incosciente",
    player_hurt_find_help_or_ems = "Premi <span class='color'>G</span> per richiedere i servizi di emergenza",
    player_hurt_time_to_live = "Perdita di sangue",
    player_hurt_auto_respawn = "Segni vitali in calo",
    player_hurt_respawn = "Premi E per vedere la luce",

    ems_online = "ASSISTENZA ONLINE",
    ems_offline = "ASSISTENZA NON DISPONIBILE",
    currently_online = "ATTUALMENTE ONLINE: "
}
