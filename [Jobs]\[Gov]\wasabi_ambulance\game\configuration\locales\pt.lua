-----------------For support, scripts, and more----------------
--------------- https://discord.gg/wasabiscripts  -------------
---------------------------------------------------------------
if Config.Language ~= 'pt' then return end

Strings = {
    no_society_account = '[^3AVISO^0] A conta da sociedade não existe para: %s',
    ems_worker = 'Trabalhador de ambulância',
    not_authorized = 'Não autorizado',
    not_authorized_desc = 'Você não está autorizado a usar isto!',
    not_on_duty = 'Fora de serviço',
    not_on_duty_desc = 'Você não está de serviço!',
    gps_enabled = 'GPS ativado',
    gps_enabled_desc = 'Você ativou o seu GPS',
    gps_disabled = 'GPS desativado',
    gps_disabled_desc = 'Você desativou o seu GPS',

    possible_cause = 'Causa possível',

    full_name = 'Nome',
    pulse = 'Pulso',
    bpm = 'BPM: %s',
    bleed_rate = 'Taxa de sangramento',
    bleed_rate_low = 'Baixo',
    bleed_rate_medium = 'Médio',
    bleed_rate_high = 'Alto',
    no_name = 'Sem nome',
    injuries = 'Lesões atuais',


    get_off_stretcher_ui = '[E] - Descer da maca',
    move = 'Mover',
    move_desc = 'Mover a maca',
    put_in_vehicle = 'Colocar no Veículo',
    stretcher_in_vehicle = 'Depositar Maca',
    put_in_vehicle_desc = 'Colocar a maca em um veículo',
    place_on_stretcher = 'Colocar Paciente',
    place_on_stretcher_desc = 'Colocar um paciente na maca',
    remove_from_stretcher = 'Remover Paciente',
    remove_from_stretcher_desc = 'Remover um paciente da maca',
    not_found = 'Não Encontrado',
    already_occupied_ambulance = 'Já Ocupada',
    already_occupied_ambulance_desc = 'Esta ambulância já está ocupada!',
    already_occupied_stretcher = 'Já Ocupada',
    already_occupied_stretcher_desc = 'Esta maca já está ocupada!',
    vehicle_occupied = 'Veículo Ocupado',
    vehicle_occupied_desc = 'Não é possível fazer isso com alguém dirigindo!',
    not_occupied_stretcher = 'Não Ocupada',
    not_occupied_stretcher_desc = 'Esta maca não está ocupada!',
    stretcher_placement_blocked = 'Colocação Bloqueada',
    stretcher_placement_blocked_desc = 'Não é possível colocar a maca aqui!',
    knocked_out = 'Nocauteado',
    knocked_out_desc = 'Você foi nocauteado!',
    checkin_cooldown = 'Tempo de Espera para Check-in',
    checkin_cooldown_desc = 'Você está tentando fazer o check-in muito rapidamente, por favor espere um momento.',
    checkingin_progress = 'Fazendo Check-in...',
    remove_dead_target = 'Remover Pessoa Inconsciente',
    ply_injury_head = 'cabeça',
    ply_injury_neck = 'pescoço',
    ply_injury_spine = 'coluna',
    ply_injury_upper = 'parte superior do corpo',
    ply_injury_lower = 'parte inferior do corpo',
    ply_injury_left_arm = 'braço esquerdo',
    ply_injury_left_leg = 'perna esquerda',
    ply_injury_right_arm = 'braço direito',
    ply_injury_right_leg = 'perna direita',
    diagnosing_patient_progress = 'Diagnosticando Paciente...',
    treating_patient_progress = 'Tratando Paciente...',
    recovering_progress = 'Recuperando...',
    injury_report = 'Relatório de Lesões',
    none = 'Nenhuma',
    mild = 'Leve',
    moderate = 'Moderada',
    severe = 'Grave',
    deadly = 'Fatal',
    injury_report_locations = 'Localização',
    injury_report_type = 'Tipo de Lesão: %s',
    injury_report_severity = 'Severidade: %s',
    injury_report_bleed = 'Perda de Sangue',
    light_injury_title = 'Lesão Leve',
    moderate_injury_title = 'Lesão Moderada',
    extreme_injury_title = 'Lesão Grave',
    injury_bleed_notify = 'Alerta de Lesão e Sangramento',
    light_injury_desc = 'Seu %s está ferido, considere visitar um médico!',
    moderate_injury_desc = 'Seu %s está muito ferido, você precisa de um médico!',
    extreme_injury_desc = 'Seu %s está extremamente ferido. Você precisa ir ao médico antes que seja tarde demais!',
    injury_msg_one = 'Você tem uma lesão significativa.',
    injury_msg_two = 'Você tem uma lesão grave.',
    injury_msg_three = 'Você tem uma lesão muito grave.',
    injury_msg_four = 'Você tem uma lesão crítica.',
    bleed_msg_one = 'Você está sangrando.',
    bleed_msg_two = 'Você está sangrando muito. Aplique pressão.',
    bleed_msg_three = 'Você vai sangrar até a morte!',
    fainted_title = 'Desmaiado',
    fainted_so_high_desc = 'Você desmaiou por estar muito alto.',
    cant_jump_title = 'Não Pode Pular!',
    cant_jump_desc = 'Você está muito ferido para tentar pular',
    blackout_title = 'Perda de Consciência',
    blackout_desc = 'Você perdeu a consciência devido à sua lesão em %s! Procure atendimento médico imediatamente!',
    treated_fully_desc = 'Você foi tratado e está se sentindo melhor do que nunca!',
    treated_not_fully_desc = 'Você foi tratado, mas precisa de tratamento adicional.',
    prescription_menu = 'Menu de Prescrições',
    prescription_menu_desc = 'Acessar e gerenciar prescrições',
    no_stretcher_detected = 'Macas Não Detectadas',
    no_stretcher_detected_desc = 'Não foi detectada nenhuma maca ativa!',
    cant_run = 'Não Pode Correr',
    cant_run_desc = 'Você está muito ferido para correr!',
    no_back_seat = 'Não há Assentos Disponíveis',
    no_back_seat_desc = 'Não há assentos disponíveis na parte de trás desta ambulância',
    enter_vehicle_back = 'Entrar na Ambulância (Traseira)',
    stretcher_already_deployed = 'Maca Já Desdobrada',
    stretcher_already_deployed_desc = 'A maca atribuída a esta ambulância já foi removida.',
    send_stretcher_home = 'Retornar ao Veículo',
    ambulance_not_found = 'A maca foi removida, mas a ambulância não foi encontrada para retornar!',
    bleedout = 'sangramento',
    player_injury_critical_desc = 'O jogador está em condição crítica devido a uma ferida %s!',
    gps_active = 'GPS Ativado',
    gps_active_desc = 'O jogador %s ativou seu GPS',
    gps_deactive = 'GPS Desativado',
    gps_deactive_desc = 'O jogador %s desativou seu GPS',
    no_wsb = '^0[^3AVISO^0] wasabi_bridge NÃO foi iniciado APÓS o framework e/ou ANTES do recurso: %s',
    spawn_blocked = 'Garagem Bloqueada',
    spawn_blocked_desc = 'Você não pode retirar seu veículo porque está bloqueado!',
    menu_remove_crutch = 'Remover Muleta',
    menu_remove_crutch_desc = 'Remova a muleta de um paciente próximo',
    menu_remove_chair = 'Remover Cadeira de Rodas',
    menu_remove_chair_desc = 'Remova uma cadeira de rodas de um paciente próximo',
    toggle_stretcher = 'Pegar maca',
    toggle_stretcher_desc = 'Pegue a maca do veículo mais próximo',
    no_vehicle_nearby = 'Veículo',
    no_vehicle_nearby_desc = 'Não há veículo próximo',
    no_ambulance_nearby_desc = 'Não há ambulância próxima',
    on_duty = 'De Serviço',
    on_duty_desc = 'Você acabou de entrar de serviço!',
    off_duty = 'Fora de Serviço',
    off_duty_desc = 'Você acabou de sair de serviço!',
    amount = 'Quantidade',
    mr = 'Sr.',
    mrs = 'Sra.',
    debt_collection = 'Cobrança de Dívidas',
    db_email =
    'Caro(a) %s %s, <br /><br />A Agência Central de Cobrança Judicial (CJCA) cobrou as multas que você recebeu da polícia.<br />Foi retirado um total de <strong>$%s</strong> da sua conta.<br /><br />Atenciosamente,<br />Sr. Wasabi',
    fine_sent = 'Multas Enviadas',
    fine_sent_desc = 'Você enviou com sucesso a multa de $%s!',
    fine_received = 'Multa Recebida',
    fine_received_desc = 'Você recebeu uma multa de $%s',
    log_killed_title = 'Jogador Morto',
    log_killed_desc =
    '> *%s [%s] matou %s [%s]\n\n**Informações do Assassino:**\nNome: `%s`\nID do Jogador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicença: `%s`\nLicença2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n**Informações da Vítima:**\nNome do Personagem: `%s`\nID do Jogador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicença: `%s`\nLicença2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n> **Causa da Morte:** `%s`',
    log_suicide_title = 'Jogador Cometeu Suicídio',
    log_suicide_desc =
    '> %s [%s] se suicidou\n\n**Informações do Jogador:**\nNome: `%s`\nID do Jogador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicença: `%s`\nLicença2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n> **Causa da Morte:** `%s`',
    unknown = 'DESCONHECIDO',
    log_admin_revive_title = 'Jogador Revivido por Admin',
    log_admin_revive_desc =
    '> *%s [%s] reviveu %s [%s] como administrador\n\n**Informações do Alvo:**\nNome: `%s`\nID do Jogador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicença: `%s`\nLicença2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||\n\n**Informações do Administrador:**\nNome do Personagem: `%s`\nID do Jogador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicença: `%s`\nLicença2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    log_self_revive_title = 'Admin Reviveu a Si Mesmo',
    log_self_revive_desc =
    '> %s [%s] reviveu a si mesmo\n\n**Informações do Jogador:**\nNome: `%s`\nID do Jogador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicença: `%s`\nLicença2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    log_death_logs = 'Registros de Morte',
    log_revive_logs = 'Registros de Revive',
    log_combat_logs = 'Registros de Combate',
    log_combatlog_title = 'Registro de Combate',
    log_combatlog_desc =
    '> %s [%s] registro de combate \n\n**Informações do jogador:**\nNome: `%s`\nID do jogador: `%s`\nIdentificador: `%s`\nSteam: `%s`\nDiscord: `%s`\nLicença: `%s`\nLicença2: `%s`\nXBL: `%s`\nFiveM: `%s`\nIP: ||%s||',
    medbag_crutch = 'Muleta',
    medbag_crutch_desc = 'Uma muleta para auxiliar pacientes a andar',
    menu_crutch = 'Aplicar Muleta',
    menu_crutch_desc = 'Adicione uma muleta a um paciente próximo',
    medbag_chair = 'Cadeira de Rodas',
    medbag_chair_desc = 'Uma cadeira de rodas para auxiliar pacientes que não conseguem andar',
    menu_chair = 'Aplicar Cadeira de Rodas',
    menu_chair_desc = 'Adicione uma cadeira de rodas a um paciente próximo',
    shot = 'tiro',
    stabbed = 'facada',
    beat = 'traumatismo por pancada',
    burned = 'queimado',
    other = 'desconhecido',
    JobMenuTitle = 'Menu EMS',
    dispatch = 'Despacho',
    dispatch_desc = 'Verifique se há jogadores precisando de assistência',
    DispatchMenuTitle = 'Despacho',
    GoBack = 'Voltar',
    key_map_text = 'Menu de Trabalho de Ambulância',
    assistance_title = 'Assistência Solicitada',
    assistance_desc = 'Uma pessoa solicitou assistência médica!',
    respawn_available_in = 'Respawn disponível em ~r~%s minutos %s segundos~s~\n',
    respawn_bleedout_in = 'Você sangrará até a morte em ~r~%s minutos %s segundos~s~\n',
    respawn_bleedout_prompt = 'Mantenha pressionado [~r~E~s~] para renascer',
    distress_send = 'Pressione [~r~G~s~] para enviar um sinal de socorro para a EMS',
    distress_sent_title = 'Assistência Solicitada',
    distress_sent_desc = 'Um sinal de socorro foi enviado para as unidades disponíveis!',
    route_set_title = 'Rota Definida',
    route_set_desc = 'Foi definida uma rota até a pessoa em perigo!',
    diagnose_patient = 'Diagnosticar',
    diagnose_patient_desc = 'Diagnostique a pessoa ferida mais próxima',
    no_requests = 'Sem pedidos ativos',
    revive_patient = 'Reanimar',
    revive_patient_desc = 'Tente reanimar a pessoa próxima',
    heal_patient = 'Curar',
    heal_patient_desc = 'Tente curar a pessoa próxima',
    sedate_patient = 'Sedação',
    sedate_patient_desc = 'Sedate temporariamente a pessoa próxima',
    drag_patient = 'Arrastar',
    drag_patient_desc = 'Arraste a pessoa ferida próxima',
    place_stretcher_target = 'Adicionar ou Remover Paciente',
    place_patient = 'Colocar no/Retirar do Veículo',
    place_patient_desc = 'Coloque a pessoa inconsciente no/fora do veículo',
    no_nearby = 'Não Encontrado',
    no_nearby_desc = 'Não parece haver ninguém por perto',
    no_injury = 'Sem Ferimentos',
    no_injury_desc = 'A pessoa não parece precisar de tratamento',
    no_injury_dead_desc =
    'O paciente não parece precisar de tratamento adicional. Tente usar o desfibrilador e esperar pelo melhor!',
    player_injury = 'Pessoa Ferida',
    player_injury_desc = 'Esta pessoa parece ter uma ferida %s',
    player_not_unconscious = 'Jogador Consciente',
    player_not_unconscious_desc = 'Parece que o jogador está consciente',
    player_unconscious = 'Jogador Inconsciente',
    player_unconscious_desc = 'O paciente precisa estar consciente para este tratamento!',
    player_reviving = 'Revivendo',
    player_reviving_desc = 'Revivendo o paciente em progresso',
    player_noitem = 'Item em Falta',
    player_noitem_desc = 'Você não tem o item necessário para isso!',
    player_successful_revive = 'Revivido',
    player_successful_revive_reward_desc = 'Você conseguiu reviver o paciente com sucesso e ganhou $%s!',
    player_successful_revive_desc = 'Você conseguiu reviver o paciente com sucesso!',
    player_healing = 'Curando',
    player_healing_desc = 'Curando o paciente em progresso',
    player_successful_heal = 'Curado',
    player_successful_heal_desc = 'Paciente curado com sucesso!',
    player_healed_desc = 'Você foi curado com sucesso pelo médico!',
    not_medic = 'Não Autorizado',
    not_medic_desc = 'Você não está treinado para usar isso!',
    target_sedated = 'Sedado',
    target_sedated_desc = 'Você foi sedado por um profissional médico',
    player_successful_sedate_desc = 'Você conseguiu sedar o paciente com sucesso',
    drop_bag_ui = '[E] - Soltar Bolsa',
    drop_stretch_ui = '[E] - Colocar Maca',
    pickup_bag_target = 'Pegar',
    move_target = 'Mover',
    interact_bag_target = 'Abrir',
    successful = 'Bem-sucedido',
    medbag_pickup = 'Você pegou a bolsa médica',
    medbag_pickup_civ = 'Você vasculhou a bolsa e pegou o que poderia ser útil',
    stretcher_pickup = 'Você enviou a maca de volta para a ambulância de onde foi retirada',
    medbag = 'Bolsa Médica',
    medbag_tweezers = 'Pinça',
    medbag_tweezers_desc = 'Usado para remover balas',
    medbag_suture = 'Kit de Sutura',
    medbag_suture_desc = 'Usado para costurar feridas',
    medbag_icepack = 'Compressa de Gelo',
    medbag_icepack_desc = 'Usado para reduzir o inchaço',
    medbag_burncream = 'Creme para Queimaduras',
    medbag_burncream_desc = 'Usado para tratar queimaduras',
    medbag_defib = 'Desfibrilador',
    medbag_defib_desc = 'Para reviver pacientes',
    medbag_medikit = 'Kit Médico',
    medbag_medikit_desc = 'Usado para curar pacientes',
    medbag_sedative = 'Sedativo',
    medbag_sedative_desc = 'Usado para sedar pacientes',
    medbag_stretcher = 'Maca Dobrável',
    medbag_stretcher_desc = 'Usado para mover pacientes',
    item_grab = 'Você pegou uma ferramenta da sua bolsa médica',
    wrong_equipment = 'Equipamento Errado!',
    wrong_equipment_desc = 'Você sequer foi treinado?',
    player_not_injured = 'Não Ferido',
    player_not_injured_desc =
    'Esta pessoa não parece precisar de nenhum tratamento adicional e está pronta para o desfibrilador',
    player_treated = 'Tratado',
    player_treated_desc = 'Você tratou o paciente com sucesso',
    revive_command_help = 'Um comando de administrador para reviver jogadores',
    revive_command_arg = 'O ID do jogador',
    reviveall_command_help = 'Um comando de administrador para reviver todos os jogadores',
    revivearea_command_help = 'Um comando de administrador para reviver jogadores próximos',
    revivearea_command_arg = 'Área para reviver jogadores',
    resetdeathcount_command_help = 'Um comando de administrador para redefinir a contagem de mortes dos jogadores',
    resetdeathcount_command_arg = 'O ID do jogador',
    viewdeathcount_command_help = 'Um comando para ver a contagem de suas mortes',
    alive_again = 'Vivo',
    alive_again_desc = 'Você foi deixado no hospital por um local!',
    request_supplies_target = 'Suprimentos Médicos',
    currency = 'R$',
    not_enough_funds = 'Fundos Insuficientes',
    not_enough_funds_desc = 'Você não tem fundos suficientes!',
    hospital_garage = 'Garagem do Hospital',
    used_meditkit = 'Kit Médico Usado',
    used_medikit_desc = 'Você conseguiu se tratar',
    action_cancelled = 'Ação Cancelada',
    action_cancelled_desc = 'Você cancelou sua última ação!',
    healing_self_prog = 'Tratando Ferimentos',
    checkin_hospital = 'Sucesso',
    checkin_hospital_desc = 'Você foi tratado com sucesso pela equipe do hospital',
    max_ems = 'Médicos Disponíveis',
    max_ems_desc = 'Há muitos médicos na cidade! Peça ajuda!',
    player_busy = 'Ocupado',
    player_busy_desc = 'Você está ocupado demais para fazer esta ação!',
    cloakroom = 'Vestiário',
    civilian_wear = 'Vestuário Civil',
    bill_patient = 'Faturar Paciente',
    bill_patient_desc = 'Envie uma fatura para um paciente próximo',
    invalid_entry = 'Entrada Inválida',
    invalid_entry_desc = 'Sua entrada foi inválida!',
    medical_services = 'Serviços Médicos',
    hospital = 'Hospital',
    interact_stretcher_ui = '[E] - Interagir',
    stretcher_menu_title = 'Interações com Maca',
    open_shop_ui = '[E] - Abrir Farmácia'

}

UIStrings = {
    player_dying = "VOCÊ ESTÁ MORRENDO",
    player_passed = "VOCÊ PASSOU",
    ems_on_the_way = "Os serviços de emergência estão a caminho!",
    press_ems_services = "para Serviços de Emergência",
    press_for_light = "para ver a luz",
    hold = "Segurar",
    time_to_respawn = "Tempo restante até o respawn",
    press = "Pressione",
    player_hurt_critical = "Condição Crítica!",
    player_hurt_severe = "Você está gravemente ferido",
    player_hurt_unconscious = "Inconsciente",
    player_hurt_unconscious_direct = "Você está inconsciente",
    player_hurt_find_help_or_ems = "Pressione <span class='color'>G</span> para solicitar serviços de emergência",
    player_hurt_time_to_live = "Sangrando até a morte",
    player_hurt_auto_respawn = "Sinais vitais diminuindo",
    player_hurt_respawn = "Pressione E para ver a luz",

    ems_online = "ASSISTÊNCIA ONLINE",
    ems_offline = "ASSISTÊNCIA INDISPONÍVEL",
    currently_online = "ATUALMENTE ONLINE: "
}
