body, html {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    color: white;
    font-weight: bold;
    font-family: "Roboto", sans-serif;
    font-style: normal;
    overflow: hidden;
    display: block;
}

@keyframes textFadeInOut {
    0%, 100% { color: transparent; }
    50% { color: white; }  /* Adjust 'black' to match the desired text color */
}

.fading-text {
    animation: textFadeInOut 2s infinite;
}

.container {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 50%;
    right: 25%;
    bottom: 15%;
    left: 25%;
    justify-content: center;
    align-items: center;
    z-index: 10; /* Ensure it's above the main content but below any modals/popups */
    border-radius: 10px;
    font-size: large;
    display: none;
}

h1 {
    padding-top: 50%;
}

h2 {
    margin-top: 4px;
    font-size: 2rem;
}

.timer {
    color: rgb(255, 0, 0);
    display: none;
}

.color {

}

.ems-counter {
    bottom: 50px;
    left: 20px;
    position: absolute;
    display: none;
    font-weight: bold;
}

.timer-box {
    display: none;
}

/* .timer-separator {
    font-size: 24px;
    color: #fff;
}

.timer-digit {
    font-size: 24px;
    color: #fff;
} */

